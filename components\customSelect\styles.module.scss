@import "../../assets/css/global.scss";

.custom_select{
    width: 100%;
    height: auto;

    label{
        display: block;
        font-size: 18px;
        font-family: $lexLight;
        margin-bottom: 5px;
    }

    select{
        width: 100%;
        font-size: 16px;
        font-family: $lexLight;
        color: #848484;
        padding: 10px 20px;
        border-radius: 4px;
        border: 1px solid #848484;
        outline: none;
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        background-image: url('https://prod-1.static.codebuckets.in/file/codebucket-production-public/codebucket-official-website/SelectIcon.webp');
        background-repeat: no-repeat;
        background-size: 4%;
        background-position: right 15px center;
    }
}

@media only screen and (max-width: 1366px){
    .custom_select{
        label{
            font-size: 16px;
            // margin-bottom: 5px;
        }
    
        select{
            font-size: 15px;
            padding: 9px 17px;
            // border-radius: 4px;
            // background-position: right 15px center;
        }
    }
}

@media only screen and (max-width: 1180px){
    .custom_select{
        label{
            font-size: 15px;
            // margin-bottom: 5px;
        }
    
        select{
            font-size: 14px;
            // padding: 9px 17px;
            // border-radius: 4px;
            // background-position: right 15px center;
        }
    }
}

@media only screen and (max-width: 980px){
    .custom_select{
        label{
            font-size: 14px;
            // margin-bottom: 5px;
        }
    
        select{
            font-size: 13px;
            padding: 8px 15px;
            // border-radius: 4px;
            // background-position: right 15px center;
        }
    }
}

@media only screen and (max-width: 780px){
    .custom_select{
        label{
            font-size: 13px;
            // margin-bottom: 5px;
        }
    
        select{
            font-size: 12px;
            padding: 6px 12px;
            // border-radius: 4px;
            // background-position: right 15px center;
        }
    }
}