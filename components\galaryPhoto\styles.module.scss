@import "../../assets/css/global.scss";

.galary_photo{
    .photo{
        width: 100%;
        height: auto;
        margin-bottom: 20px;
    }

    .view_link{
        display: flex;
        align-items: center;
        justify-content: flex-end;
        font-size: 14px;
        font-family: $lexRegular;
        color: white;
        padding-right: 20px;

        img{
            width: 10px;
            height: auto;
            margin-left: 15px;
        }
    }
}

@media only screen and (max-width:  667px){
    .galary_photo{
        .photo{
            width: 100%;
            height: auto;
            margin-bottom: 1.3vw;
        }
    
        .view_link{
            font-size: 3vw;
            padding-right: 3vw;
    
            img{
                width: 2vw;
                height: auto;
                margin-left: 2vw;
            }
        }
    }   
}