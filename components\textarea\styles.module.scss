@import "../../assets/css/global.scss";

.textarea_container{
    width: 100%;
    height: auto;

    label{
        display: block;
        font-size: 18px;
        font-family: $lexLight;
        margin-bottom: 5px;
    }

    textarea{
        width: 100%;
        font-size: 16px;
        font-family: $lexLight;
        color: #848484;
        padding: 11px 20px;
        border-radius: 4px;
        border: 1px solid #848484;
        outline: none;
        resize: none;
        height: 250px;

        &::placeholder{
            font-size: inherit;
            font-family: inherit;
            color: inherit;
        }
    }
}


@media only screen and (max-width: 1366px){
    .textarea_container{
        label{
            font-size: 16px;
            // margin-bottom: 5px;
        }
    
        textarea{
            font-size: 15px;
            padding: 9px 17px;
            // border-radius: 4px;
            height: 210px;
        }
    }
}

@media only screen and (max-width: 1180px){
    .textarea_container{
        label{
            font-size: 15px;
            // margin-bottom: 5px;
        }
    
        textarea{
            font-size: 14px;
            padding: 11px 20px;
            // border-radius: 4px;
            height: 190px;
        }
    }
}

@media only screen and (max-width: 980px){
    .textarea_container{
        label{
            font-size: 14px;
            // margin-bottom: 5px;
        }
    
        textarea{
            font-size: 13px;
            padding: 8px 16px;
            // border-radius: 4px;
            height: 140px;
        }
    }
}

@media only screen and (max-width: 780px){
    .textarea_container{
        label{
            font-size: 13px;
            // margin-bottom: 5px;
        }
    
        textarea{
            font-size: 12px;
            padding: 6px 12px;
            // border-radius: 4px;
            height: 130px;
        }
    }
}
