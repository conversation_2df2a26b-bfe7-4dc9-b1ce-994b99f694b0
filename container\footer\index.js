import { useRef } from 'react';
import classes from "./styles.module.scss";
import Image from "next/image";
import Button from "../../components/button";
import { useRouter } from "next/router";
import { Swiper, SwiperSlide } from "swiper/react";
import ClientSaysAboutUsCard from '@/components/clientSayABoutUsCard';
import 'swiper/css';
import 'swiper/css/free-mode';
import 'swiper/css/pagination';
import Link from 'next/link';

const Footer = () => {

    const router = useRouter();
    const isCurrentPage = router.pathname === "/career";

    const swiperRef = useRef(null);

    const handlePrev = () => {
        if (swiperRef.current) {
            swiperRef.current.slidePrev();
        }
    };
 
    const handleNext = () => {
        if (swiperRef.current) {
            swiperRef.current.slideNext();
        }
    };  

    return(
        <div className={`${classes.footer_containers} ${isCurrentPage ? classes.bg_image : ''}`}>
            <div className={classes.footer_content}>        

                {!isCurrentPage && (
                        
                    <>
                        <div className={classes.footer_header}>
                            <p className={classes.footer_main_header}>
                                What our clients say About us
                            </p>
                            <p className={classes.know_Abt_our_client}>
                                Know about our clients, we are a worldwide corporate brand
                            </p>
                            <div className={classes.hr_line}></div>
                            </div>

                                <div className={classes.wish_a_dish}>
                                    <Swiper
                                        onSwiper={(swiper) => {
                                            swiperRef.current = swiper;
                                        }}
                                        slidesPerView={1}
                                        spaceBetween={0}
                                        freeMode={true}
                                        loop={true}
                                    >
                                        <SwiperSlide>
                                            <ClientSaysAboutUsCard 
                                                client_image={`${process.env.NEXT_PUBLIC_CDN_URL}wishADish.webp`}
                                                description="The wonderful team effort of Codebucket Solutions helped me create new hopes for
                                                    my business. With their professionalism, prompt response and courteous service,
                                                    I was able to grow my business at a brisk pace. Experience was positive and unsurpassed. Doing business has never been so pleasant!"
                                            />
                                        </SwiperSlide>
                                        <SwiperSlide>
                                            <ClientSaysAboutUsCard
                                                client_image={`${process.env.NEXT_PUBLIC_CDN_URL}rnPlus.webp`}
                                                description="
                                                    codebucket keeps doing what comes naturally to them which is building teams with
                                                    their clients, partners and creating exceptional software development and
                                                    support. Job well done."
                                            />
                                        </SwiperSlide>
                                        <SwiperSlide>
                                            <ClientSaysAboutUsCard
                                                client_image={`${process.env.NEXT_PUBLIC_CDN_URL}pljr.webp`}
                                                description="
                                                    We thank Codebucket Solutions for the wonderful job in helping us develop our
                                                    program. Everyone was professional, excellent and hard working. Thanks to them,
                                                    we were able to achieve our goal on time, and we look forward to continue
                                                    working with them in the future."
                                            />
                                        </SwiperSlide>
                                        <SwiperSlide>
                                            <ClientSaysAboutUsCard
                                                client_image={`${process.env.NEXT_PUBLIC_CDN_URL}trouvaille.webp`}
                                                description="
                                                    Codebucket has really helped us in growing our business and are really
                                                    professional when it comes to developing softwares and providing client
                                                    support.Would highly recommend codebucket for any kind of business solution."
                                            />
                                        </SwiperSlide>
                                      
                                    </Swiper>
                                    <div className={classes.navigation}>
                                        <Button 
                                            icon={<Image src={`${process.env.NEXT_PUBLIC_CDN_URL}prev.webp`} width={44} height={44} alt="nav button"/>} 
                                            onClick={handlePrev} 
                                            second_class={classes.navigation_button}
                                        />
                                        <Button 
                                            icon={<Image src={`${process.env.NEXT_PUBLIC_CDN_URL}next.webp`} width={44} height={44} alt="nav button"/>} 
                                            onClick={handleNext} 
                                            second_class={classes.navigation_button}
                                        />
                                    </div>
                                </div>

                            <div className={classes.lets_talk}>
                                <div className={classes.left_text}>
                                    <p className={classes.big}>Let&apos;s Connect </p>
                                    <p className={classes.small}>We would be delighted to discuss your vision and see how we can support you in achieving it.</p>
                                </div>
                                {/* <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}redirectArrow.webp`} width={65} height={65} alt="arrow" /> */}
                            </div>

                            <div className={classes.hr_line}></div>
                        </>    
                    )}

                    {isCurrentPage && (
                        <div className={classes.for_career_footer}>
                            
                            <p className={classes.sounds_good_header}>Sounds Good?</p>
                            
                            <p className={classes.join_text}>Join Codebucket</p>
                            
                            <Button 
                                button_text="Click to View Jobs" 
                                variant="button_orange" 
                                second_class={classes.view_job_button}
                                onClick={() => window.open('https://hrms.codebuckets.in/jobs', '_blank', 'noopener,noreferrer')}
                            />
                        </div>
                    )}

                    

                    <div className={classes.support_help_line}>
                        <div className={classes.left_contact}>
                            <p><EMAIL></p>
                            <p>+91 99950 08671</p>
                        </div>
                    </div>

                    <div className={classes.footer_menus_and_others}>
                        <div className={classes.left_menu}>
                            <ul>
                                <Link href={"/"}>
                                    <li>Home</li>
                                </Link>
                                <Link href={"/product"}>
                                    <li>Products</li>
                                </Link>
                                <Link href={"https://codebucketlab.com/"} target='_blank'>
                                    <li>cB Lab</li>
                                </Link>
                                <Link href={"/career"}>
                                    <li>Careers</li>
                                </Link>
                            </ul>
                        </div>

                        <div className={classes.others_right}>
                            <div className={classes.others}>
                                <p className={classes.head_text}>Registered Office</p>
                                <p className={classes.desc_text}>HMT Colony, Kalamassery, Kochi, Kerala.</p>
                            </div>
                            <div className={classes.others}>
                                <p className={classes.head_text}>Bangalore</p>
                                <p className={classes.desc_text}>WeWork Prestige Central, 36, Infantry Road, Bangalore, Karnantaka 560001</p>
                            </div>
                            <div className={classes.others}>
                                <p className={classes.head_text}>Patna</p>
                                <p className={classes.desc_text}>Opposite Gate No 93, Digha Danapur Road, Patna - 800013</p>
                            </div>
                            <div className={classes.others}>
                                <Link href={"https://www.facebook.com/codebuckets"} target='_blank'>
                                    <Image className={classes.icons} src={`${process.env.NEXT_PUBLIC_CDN_URL}blackFBNew.webp`} width={40} height={40} alt="" />
                                </Link>
                                <Link href={"https://www.instagram.com/codebuckets_/"} target='_blank'>
                                    <Image className={classes.icons} src={`${process.env.NEXT_PUBLIC_CDN_URL}blackInstagram.webp`} width={40} height={40} alt="" />
                                </Link>
                                <Link href={"https://x.com/codebuckets_"} target='_blank'>
                                    <Image className={classes.icons} src={`${process.env.NEXT_PUBLIC_CDN_URL}blackTwitter.webp`} width={40} height={40} alt="" />
                                </Link>
                                <Link href={'https://www.linkedin.com/company/13441306'} target='_blank'>
                                    <Image className={classes.icons} src={`${process.env.NEXT_PUBLIC_CDN_URL}blackLinkedIn.webp`} width={40} height={40} alt="" />
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    )
}

export default Footer;