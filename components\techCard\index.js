import classes from "./styles.module.scss";
import PropTypes from 'prop-types';

const TechCard = (props) => {
    return(
        <div className={classes.technology_card}>
            <div className={classes.card_image}>
                {props.image}
            </div>
            <p className={classes.card_text}>{props.tech_brand}</p>
        </div>
    )
}
TechCard.propTypes = {
    image: PropTypes.object.isRequired,
    tech_brand: PropTypes.string.isRequired,
};

export default TechCard;