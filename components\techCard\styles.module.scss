@import "../../assets/css/global.scss";

.technology_card{
    min-width: 140px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    border: 1px solid rgba(0, 0, 0, 0.5);
    padding: 10px 20px;
    border-radius: 50px;

    .card_image{
        display: flex;
        align-items: center;
        margin-right: 10px;

        img{
            width: 25px;
            height: auto;
        }
    }

    .card_text{
        font-size: 16px;
        font-family: $lexRegular;
        user-select: none;
        white-space: nowrap;
    }
}

.technology_card:last-child{
    cursor: pointer;
}


@media only screen and (max-width: 1366px){
    .technology_card{
        min-width: auto;
        padding: 8px 16px;
    
        .card_image{
            margin-right: 7px;

            img{
                width: 20px;
            }
        }
    
        .card_text{
            font-size: 15px;
        }
    }   
}

@media only screen and (max-width: 1180px){
    .technology_card{
        padding: 7px 14px;
    
        .card_image{
            margin-right: 6px;

            img{
                width: 17px;
            }
        }
    
        .card_text{
            font-size: 14px;
        }
    }   
}

@media only screen and (max-width: 1024px){
    .technology_card{
        padding: 1vw 2.2vw;
        gap: 1.5vw;

        .card_image{
            margin-right: unset;

            img{
                width: 3.6vw;
            }
        }
        .card_text{
            font-size: 2.4vw;
        }
    }
}

@media only screen and (max-width: 667px){
    .technology_card{
        padding: 1.2vw 2.2vw;
        gap: 1.5vw;

        .card_image{
            img{
                width: 4vw;
            }
        }
        .card_text{
            font-size: 2.8vw;
        }
    }
}