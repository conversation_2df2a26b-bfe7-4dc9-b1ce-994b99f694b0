@import "../../../assets/css/global.scss";

.why_choose_us_container{
    width: 100%;
    height: auto;
    
    .why_choose_us_content{
        background-color: black;
        width: 100%;

        .content{
            width: 100%;
            height: 100vh;
            position: sticky;
            top: 0;

            .framer_wrapper{
                .animation_content{
                    position: relative;
                    width: 100%;
                    height: 100vh;  
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    .content_header{
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: auto;
                        background-color: #151514;
                        padding: 60px 30px 30px;
        
                        .header{
                            text-align: center;
                            color: white;
                            font-size: 30px;
                            font-family: $ubuntuBold;
                            margin-bottom: 10px;
                        }
                        .desc_text{
                            text-align: center;
                            color: white;
                            font-size: 20px;
                            font-family: $lexRegular;
                        }
                    }

                    .center_content{
                        width: 100%;
                        height: auto;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
        
                        .left_content{
                            width: 40%;
                            display: flex;
                            justify-content: center;
                            
                            .left_content_header{
                                color: white;
                                font-size: 72px;
                                font-family: $ubuntuMedium;
                            }
                        }
                            
                        .right_content{
                            width: 50%;
                            background-color: #141414;
                            border-top-left-radius: 16px;
                            border-bottom-left-radius: 16px;
                            padding: 60px 80px;
                            
                            .custom_list{
                                display: flex;
                                margin-bottom: 30px;
                            
                                &:last-child{
                                    margin-bottom: unset;
                                }
                            
                                .numbering{
                                    font-size: 20px;
                                    font-family: $lexRegular;
                                    color: white;
                                    margin-right: 30px;
                                }
                            
                                .text{
                                    font-size: 20px;
                                    font-family: $lexRegular;
                                    color: white;
                            
                                    .green{
                                        color: #DBFF8F;
                                    }
                            
                                    .orange{
                                        color: #FF8F33;
                                    }
                                }
                            }
                        }
                    }


                    .status_bar{
                        padding:  0px 40px 60px;
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        right: 0;
                        
                        .bar{
                            width: 100%;
                            height: 1px;
                            background-color: white;
                        }
                        
                        .status{
                            width: 100%;
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                            gap: 20px;
                            margin-top: -4px;
                        
                            .indicator{
                                width: 100%;
                                height: 7px;
                                background-color: transparent;
                                border-radius: 5px;
                                position: relative;

                                &.active{
                                    background-color: white;
                                }
                        
                                p{
                                    color: white;
                                    font-size: 18px;
                                    font-family: $lexSemiBold;
                                    position: absolute;
                                    right: 0;
                                    top: -25px;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@media  only screen and (max-width: 1440px){
    .why_choose_us_container{
        .why_choose_us_content{
            .content{
                .framer_wrapper{
                    .animation_content{
                        .content_header{
                            padding: 60px 20px 20px;
            
                            .header{
                                font-size: 26px;
                                margin-bottom: 10px;
                            }
                            .desc_text{
                                font-size: 18px;
                            }
                        }
    
                        .center_content{
                            .left_content{
                                width: 40%;
                                .left_content_header{
                                    font-size: 66px;
                                }
                            }
                                
                            .right_content{
                                border-top-left-radius: 16px;
                                border-bottom-left-radius: 16px;
                                padding: 50px 70px;
                                
                                .custom_list{
                                    margin-bottom: 20px;
                                
                                    .numbering{
                                        font-size: 18px;
                                        margin-right: 25px;
                                    }
                                
                                    .text{
                                        font-size: 18px;
                                    }
                                }
                            }
                        }
    
    
                        .status_bar{
                            padding:  0px 30px 50px;
                            
                            .status{
                                gap: 20px;
                            
                                .indicator{
                                    height: 6px;

                                    p{
                                        font-size: 16px;
                                        top: -23px;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@media  only screen and (max-width: 1366px){
    .why_choose_us_container{
        .why_choose_us_content{
            .content{
                .framer_wrapper{
                    .animation_content{
                        .content_header{
                            padding: 55px 20px 20px;
            
                            .header{
                                font-size: 24px;
                                margin-bottom: 7px;
                            }
                            .desc_text{
                                font-size: 16px;
                            }
                        }
    
                        .center_content{
                            .left_content{
                                .left_content_header{
                                    font-size: 56px;
                                }
                            }
                                
                            .right_content{
                                padding: 40px 60px;
                                
                                .custom_list{
                                    .numbering{
                                        font-size: 16px;
                                        margin-right: 20px;
                                        font-family: $lexLight;
                                    }
                                
                                    .text{
                                        font-size: 16px;
                                        font-family: $lexExtraLight;
                                        letter-spacing: 1px;
                                    }
                                }
                            }
                        }
    
    
                        .status_bar{
                            padding:  0px 30px 40px;
                            
                            .status{
                                .indicator{
                                    p{
                                        font-size: 15px;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 1280px){
    .why_choose_us_container{
        .why_choose_us_content{
            .content{
                .framer_wrapper{
                    .animation_content{
                        .content_header{
                            padding: 55px 20px 15px;
            
                            .header{
                                font-size: 20px;
                                margin-bottom: 5px;
                            }
                            .desc_text{
                                font-size: 14px;
                                font-family: $lexLight;
                                letter-spacing: 0.3px;
                            }
                        }
    
                        .center_content{
                            .left_content{
                                width: 45%;

                                .left_content_header{
                                    font-size: 50px;
                                }
                            }
                                
                            .right_content{
                                padding: 40px 50px;
                                
                                .custom_list{
                                    .numbering{
                                        font-size: 14px;
                                    }
                                
                                    .text{
                                        font-size: 14px;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 667px){
    .why_choose_us_container{
        .why_choose_us_content{
            .content{
                .framer_wrapper{
                    .animation_content{
                        .content_header{
                            padding: 45px 15px 15px;
            
                            .header{
                                font-size: 5vw;
                                margin-bottom: 1.5vw;
                            }
                            .desc_text{
                                font-size: 3.7vw;
                            }
                        }
    
                        .center_content{
                            justify-content: center;
                            flex-direction: column;
                            gap: 4vh;
            
                            .left_content{
                                width: 100%;
                                
                                .left_content_header{
                                    font-size: 8vw;
                                    text-align: center;
                                }
                            }
                                
                            .right_content{
                                width: 100%;
                                border-radius: unset;
                                padding: 15px;
                                
                                .custom_list{
                                    margin-bottom: 3vh;
                                
                                    .numbering{
                                        font-size: 3vw;
                                        margin-right: 4vw;
                                    }
                                
                                    .text{
                                        font-size: 3.4vw;
                                    }
                                }
                            }
                        }
    
    
                        .status_bar{
                            padding:  0px 40px 60px;
                            position: absolute;
                            bottom: 0;
                            left: 0;
                            right: 0;
                            
                            .bar{
                                width: 100%;
                                height: 1px;
                                background-color: white;
                            }
                            
                            .status{
                                width: 100%;
                                display: flex;
                                align-items: center;
                                justify-content: space-between;
                                gap: 20px;
                                margin-top: -4px;
                            
                                .indicator{
                                    width: 100%;
                                    height: 7px;
                                    background-color: transparent;
                                    border-radius: 5px;
                                    position: relative;
    
                                    &.active{
                                        background-color: white;
                                    }
                            
                                    p{
                                        color: white;
                                        font-size: 18px;
                                        font-family: $lexSemiBold;
                                        position: absolute;
                                        right: 0;
                                        top: -25px;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}