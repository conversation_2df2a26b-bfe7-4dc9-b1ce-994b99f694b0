$white: white;
$black: black;


$font16: 16px;


$lexThin: LexThin;
$lexExtraLight: LexExtraLight;
$lexLight: LexLight;
$lexRegular: LexRegular;
$lexMedium: LexMedium;
$lexSemiBold: LexSemiBold;
$lexBold: LexBold;
$lexExtraBold: LexExtraBold;
$lexBlack: LexBlack;


@font-face {
    font-family: LexThin;
    src: url('../fonts/LEXEND/static/Lexend-Thin.ttf');
}

@font-face {
    font-family: LexExtraLight;
    src: url('../fonts/LEXEND/static/Lexend-ExtraLight.ttf');
}

@font-face {
    font-family: LexLight;
    src: url('../fonts/LEXEND/static/Lexend-Light.ttf');
}

@font-face {
    font-family: LexRegular;
    src: url('../fonts/LEXEND/static/Lexend-Regular.ttf');
}

@font-face {
    font-family: LexMedium;
    src: url('../fonts/LEXEND/static/Lexend-Medium.ttf');
}

@font-face {
    font-family: LexSemiBold;
    src: url('../fonts/LEXEND/static/Lexend-SemiBold.ttf');
}

@font-face {
    font-family: LexBold;
    src: url('../fonts/LEXEND/static/Lexend-Bold.ttf');
}

@font-face {
    font-family: LexExtraBold;
    src: url('../fonts/LEXEND/static/Lexend-ExtraBold.ttf');
}

@font-face {
    font-family: LexBlack;
    src: url('../fonts/LEXEND/static/Lexend-Black.ttf');
}

$ubuntuBold: UbuntuBold;
$ubuntuLight: UbuntuLight;
$ubuntuMedium: UbuntuMedium;
$ubuntuRegular: UbuntuRegular;

@font-face {
    font-family: UbuntuBold;
    src: url('../fonts/Ubuntu/Ubuntu-Bold.ttf');
}

@font-face {
    font-family: UbuntuLight;
    src: url('../fonts/Ubuntu/Ubuntu-Light.ttf');
}

@font-face {
    font-family: UbuntuMedium;
    src: url('../fonts/Ubuntu/Ubuntu-Medium.ttf');
}

@font-face {
    font-family: UbuntuRegular;
    src: url('../fonts/Ubuntu/Ubuntu-Regular.ttf');
}