@import "../../../assets/css/global.scss";

.location_container{
    width: 100%;
    height: auto;
    margin-top: 100px;
    background-color: black;
    border-radius: 30px;
    overflow: hidden;

    .location_content{
        width: 100%;
        height: auto;
        display: flex;
        justify-content: flex-start;
        gap: 30px;

        .embed_map_link{
            width: 100%;
            height: 100%;
            opacity: 0.4;
            outline: none;
            border: none;

            &:hover{
                opacity: 0.8;
            }
        }
    }
}

@media only screen and (max-width: 667px){
    .location_container{
        margin-top: 40px;
        border-radius: 3vw;

        .location_content{
            flex-direction: column;
            gap: 3vw;
        }
    }
}