import classes from "./styles.module.scss";
import Image from "next/image";
import CountUp from 'react-countup';
import { useInView } from 'react-intersection-observer';
import Cmmi from "../../../assets/images/cmmi2.webp";

const SatisfiedResult = () => {

    const { ref, inView } = useInView({
        triggerOnce: true,
        threshold: 0.1,
    });

    return(
        <div className={classes.satisfied_result}>
            <p className={classes.satisfied_result_header}>
                A Legacy of Satisfied Clients & Exceptional Results
            </p>
                
            <div className={classes.results_over_view_cards}>
                <div className={classes.cards_wrapper}>
                    <div className={classes.card} ref={ref}>
                        {
                            inView ? (
                                <p className={classes.number}>
                                    <CountUp start={0} end={42} duration={3} delay={0.5} />+
                                </p>
                            ) : (
                            <p></p>
                            )
                        }
                        <p className={classes.text}>Happy Clients</p>
                    </div>
                </div>
                <div className={classes.cards_wrapper}>
                    <div className={classes.card} ref={ref}>
                        {
                            inView ? (
                                <p className={classes.number}>
                                    <CountUp start={0} end={55} duration={3} delay={0.5} />+
                                </p>
                            ) : (
                            <p></p>
                            )
                        }
                        <p className={classes.text}>Project <br /> Completed</p>
                    </div>
                </div>
                <div className={classes.cards_wrapper}>
                    <div className={classes.card} ref={ref}>
                        {
                            inView ? (
                                <p className={classes.number}>
                                    <CountUp start={0} end={15} duration={3} delay={0.5} />+
                                </p>
                            ) : (
                            <p></p>
                            )
                        }
                        <p className={classes.text}>Experience</p>
                    </div>
                </div>
                <div className={classes.cards_wrapper}>
                    <div className={classes.card} ref={ref}>
                        {
                            inView ? (
                                <p className={classes.number}>
                                    <CountUp start={0} end={60} duration={3} delay={0.5} />+
                                </p>
                            ) : (
                            <p></p>
                            )
                        }
                        <p className={classes.text}>Resources</p>
                    </div>
                </div>
            </div>
                

            <div className={classes.re_exp_header}>
                <p className={classes.expertise_header}>
                    Recognition and <br /><span>Expertise</span>
                </p>

                <p className={classes.some_text}>Here are some Recognition and Expertise</p>
            </div>

            <div className={classes.company_names}>
                <div className={classes.company_name_cards}>
                    <Image src={Cmmi} width={410} height={300} alt="icon" />
                </div>
                 <div className={classes.company_name_cards}>
                    <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}iso.webp`} width={410} height={300} alt="icon" />
                </div>
                <div className={classes.company_name_cards}>
                    <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}kenoics.webp`} width={410} height={275} alt="icon" />
                </div>
                <div className={classes.company_name_cards}>
                    <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}symbol.webp`} width={410} height={300} alt="icon" />
                </div>
            </div>
        </div>
    )
} 

export default SatisfiedResult;