import classes from "./styles.module.scss";
import PropTypes from 'prop-types';

const Input = ({
  type,
  value,
  onChange,
  placeholder,
  label,
  second_class,
  star,
  options_hint,
  hint,
  name,
  required,
  disabled,
  min,
  max,
  inputMode,
  maxLength,
  pattern,
  onKeyDown,
  onPaste,
}) => {
  return (
    <div className={`${classes.input_types} ${second_class}`}>
      <label htmlFor={label}>
        {label}
        <span className={classes.options_hint}>{options_hint}</span> <span>{star}</span>
      </label>
      <input
        type={type}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        id={label}
        required={required || star}
        name={name}
        disabled={disabled}
        min={min}
        max={max}
        inputMode={inputMode}
        maxLength={maxLength}
        pattern={pattern}
        onKeyDown={onKeyDown}
        onPaste={onPaste}
      />
      <p className={classes.hint}>{hint}</p>
    </div>
  );
};

Input.propTypes = {
  type: PropTypes.string.isRequired,
  value: PropTypes.any.isRequired,
  onChange: PropTypes.func.isRequired,
  placeholder: PropTypes.string,
  label: PropTypes.string.isRequired,
  second_class: PropTypes.string,
  star: PropTypes.node,
  options_hint: PropTypes.string,
  hint: PropTypes.string,
  name: PropTypes.string,
  required: PropTypes.bool,
  disabled: PropTypes.bool,
  min: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  max: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  inputMode: PropTypes.string,
  maxLength: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  pattern: PropTypes.string,
  onKeyDown: PropTypes.func,
  onPaste: PropTypes.func,
};

export default Input;