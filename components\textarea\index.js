import classes from "./styles.module.scss";
import PropTypes from 'prop-types';

const Textarea = ({ label, value, onChange, placeholder, name }) => {
    return (
      <div className={classes.textarea_container}>
        {label && <label className={classes.label}>{label}</label>}
        <textarea
            className={classes.textarea}
            name={name}
            value={value}
            onChange={onChange}
            placeholder={placeholder}
        />
      </div>
    );
};

Textarea.propTypes = {
    label: PropTypes.string,
    value: PropTypes.string.isRequired,
    onChange: PropTypes.func.isRequired,
    placeholder: PropTypes.string,
    name: PropTypes.string,
    rows: PropTypes.number,
    cols: PropTypes.number,
};

Textarea.defaultProps = {
    label: '',
    placeholder: '',
    name: '',
};

export default Textarea;
