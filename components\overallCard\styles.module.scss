@import "../../assets/css/global.scss";

.company_overall_status_card{
    border-radius: 8px;
    box-shadow: 0px 2px 5px rgb(223, 223, 223);
    background-color: white;
    padding: 15px 25px;
    user-select: none;

    .number_count{
        font-size: 30px;
        font-family: $lexRegular;
        color: black;
    }
    .text{
        font-size: 20px;
        font-family: $lexLight;
    }

    &:hover{
        .number_count{
            color: #ffa013;
        }
    }
}

@media only screen and (max-width: 1366px){
    .company_overall_status_card{
        border-radius: 6px;
        padding: 12px 22px;
    
        .number_count{
            font-size: 25px;
        }
        .text{
            font-size: 16px;
        }
    }   
}

@media only screen and (max-width: 1180px){
    .company_overall_status_card{
        padding: 10px 20px;
    
        .number_count{
            font-size: 20px;
        }
        .text{
            font-size: 14px;
        }
    }   
}

@media only screen and (max-width: 980px){
    .company_overall_status_card{
        padding: 8px 16px;
    
        .number_count{
            font-size: 17px;
        }
        .text{
            font-size: 13px;
        }
    }   
}

@media only screen and (max-width: 780px){
    .company_overall_status_card{
        padding: 6px 12px;
    
        .number_count{
            font-size: 15px;
        }
        .text{
            font-size: 11px;
        }
    }   
}

@media only screen and (max-width: 667px){
    .company_overall_status_card{
        padding: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
    
        .number_count{
            font-size: 4vw;
        }
        .text{
            font-size: 2.9vw;
        }
    }   
}