name: SonarQube PR Scan

on:
  # Trigger analysis on PR open/update
  pull_request:
    types: [opened, synchronize, reopened]

  # Manual trigger with optional project_key
  workflow_dispatch:

jobs:
  SonarQube-QualityGate:
    name: SonarQube-QualityGate
    runs-on: sonarqube-scanner

    permissions:
      contents: read
      pull-requests: write

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Required for proper Sonar analysis

      - name: Run SonarQube Scan
        uses: sonarsource/sonarqube-scan-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
