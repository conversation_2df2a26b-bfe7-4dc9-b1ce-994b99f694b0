import PropTypes from 'prop-types';
import classes from "./styles.module.scss";
import Image from "next/image";

const ClientSaysAboutUsCard = (props) => {
    return (
        <div className={classes.client_says_about_us}>
            <div className={classes.logo}>
                <Image src={props.client_image} width={300} height={260} alt="logo" />
            </div>
            <div className={classes.description}>
                <p>{props.description}</p>
            </div>
        </div>
    );
};

ClientSaysAboutUsCard.propTypes = {
    client_image: PropTypes.string.isRequired,
    description: PropTypes.string.isRequired,
};

export default ClientSaysAboutUsCard;
