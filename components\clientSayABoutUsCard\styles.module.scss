@import "../../assets/css/global.scss";

.client_says_about_us{
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .logo{
        width: 310px;
        min-width: 310px;
        height: 260px;
        overflow: hidden;
        background-color: white;

        img{
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
    }

    .description{
        width: calc(100% - 310px);
        height: unset;
        overflow: hidden;
        display: flex;
        align-items: center;
        padding: 0px 50px;
        background-color: white;

        p{
            font-size: 20px;
            line-height: 30px;
            font-family: $lexLight;
        }
    }
}

@media only screen and (max-width: 1366px){
    .client_says_about_us{
        .logo{
            width: 280px;
            min-width: 280px;
            height: 240px;
            overflow: hidden;
        }

        .description{
            width: calc(100% - 280px);
            padding: 0px 35px;

            p{
                font-size: 17px;
                line-height: 27px;
            }
        }
    }
}

@media only screen and (max-width: 667px){
    .client_says_about_us{
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-direction: column;
    
        .logo{
            width: 60%;
            min-width: unset;
            height: auto;
            overflow: hidden;
            margin-bottom: 20px;
            aspect-ratio: 2/2;
        }
    
        .description{
            width: 100%;
            height: auto;
            overflow: hidden;
            display: flex;
            align-items: center;
            padding: unset;
    
            p{
                font-size: 3.8vw;
                line-height: 5.4vw;
                font-family: $lexLight;
            }
        }
    }
}