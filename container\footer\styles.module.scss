@import "../../assets/css/global.scss";

.footer_containers{
    width: 100%;
    min-height: 100vh;
    background-color: $black;
    padding: 80px 40px 40px;

    &.bg_image{
        background-image: url('https://prod-1.static.codebuckets.in/file/codebucket-production-public/codebucket-official-website/Ellipse%202161.webp');
        background-repeat: no-repeat;
        background-position: center 0px;
        background-size:  85% auto;
    }

    .footer_content{
        width: 100%;
        height: auto;

        .hr_line{
            width: 100%;
            height: 1px;
            background-color: #FC9700;
            margin-bottom: 50px;
        }

        .footer_header{
            width: 100%;
            height: auto;
            overflow: hidden;
            .footer_main_header{
                color: $white;
                font-size: 60px;
                font-family: $ubuntuLight;
                margin-bottom: 20px;
            }
    
            .know_Abt_our_client{
                font-size: 19px;
                color: $white;
                font-family: $lexLight;
                margin-bottom: 20px;
            }
        }

        .wish_a_dish{
            background-color: white;
            padding: 40px;
            border-radius: 16px;
            overflow: hidden;
            margin-bottom: 70px;

            .navigation{
                display: flex;
                justify-content: flex-end;
                gap: 10px;

                .navigation_button{
                    margin: 0;padding: 0;

                    img{
                        background-color: white;
                        border-radius: 5px;
                        width: 50px;
                        height: 35px;
                        object-fit: cover;
                        cursor: pointer;
                    }
    
                    img:hover{
                        opacity: 0.7;
                    }
                }
            }
        }

        .lets_talk{
            width: 100%;
            height: auto;
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            margin-bottom: 20px;

            .left_text{
                .big{
                    font-size: 128px;
                    font-family: $ubuntuLight;
                    color: $white;
                }
                .small{
                    font-size: 18px;
                    font-family: $lexRegular;
                    color: $white;
                }
            }

            img{
                width: 65px;
                height: 65px;
                margin: 40px 40px 0px 0px;
            
            }
        }

        .for_career_footer{
            width: 100%;
            height: auto;
            padding-top: 100px;
            padding-bottom: 90px;

            .sounds_good_header{
                color: white;
                text-align: center;
                font-family: $ubuntuLight;
                font-size: 128px;
            }
    
            .join_text{
                color: white;
                text-align: center;
                font-family: $lexRegular;
                font-size: 48px;
                margin-top: -15px;
                margin-bottom: 50px;
            }
    
            .view_job_button{
                position: relative;
                left: 50%;
                transform: translate(-50%);
            }
        }

        .support_help_line{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 50px;

            .left_contact{
                p{
                    font-family: $lexLight;
                    color: white;
                    font-style: italic;
                    margin-bottom: 6px;
                    font-size: 18px;
                }
            }
        }

        .footer_menus_and_others{
            display: flex;
            align-items: flex-start;
            justify-content: space-between;

            .left_menu{
                ul{
                    list-style-type: none;
                    li{
                        color: white;
                        font-size: 23px;
                        font-family: $lexLight;
                        margin-bottom: 16px;
                        transition: 200ms;

                        &:hover{
                            padding-left: 10px;
                        }
                    }
                }
            }

            .others_right{
                width: 600px;
                display: flex;
                flex-wrap: wrap;
                gap: 24px 30px;
                

                .others{
                    width: calc(50% - 15px);
                    height: auto;

                    .head_text{
                        color: white;
                        font-size: 18px;
                        font-family: $lexRegular;
                        margin-bottom: 7px;
                    }

                    .desc_text{
                        color: rgba(255, 255, 255, 0.8);
                        font-size: 15px;
                        font-family: $lexLight;
                    }

                    a{
                        background-color: white;
                        width: 35px;
                        min-width: 35px;
                        height: 35px;
                        border-radius: 50%;
                        margin-right: 10px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        float: left;
                        position: relative;
                        top: 50%;
                        transform: translateY(-50%);
                        
                        img{
                            width: auto;
                            height: 55%;
                        }
                    }
                }
            }
        }
    }
}


@media only screen and (max-width: 1366px){
    .footer_containers{
        padding: 72px 36px 40px;

        .footer_content{
            .hr_line{
                margin-bottom: 45px;
            }

            .footer_header{
                .footer_main_header{
                    font-size: 52px;
                    margin-bottom: 17px;
                }

                .know_Abt_our_client{
                    font-size: 17px;
                }
            }

            .wish_a_dish{
                padding: 25px;
                border-radius: 14px;
                margin-bottom: 60px;

                .navigation{
                    .navigation_button{
                        img{
                            width: 45px;
                        }
                    }
                }
            }

            .lets_talk{
                margin-bottom: 17px;

                .left_text{
                    .big{
                        font-size: 110px;
                    }

                    .small{
                        font-size: 16px;
                    }
                }

                img{
                    width: 55px;
                    height: auto;
                    margin: 40px 35px 0px 0px;
                }
            }

            .for_career_footer{
                padding-top: 80px;
                padding-bottom: 70px;
    
                .sounds_good_header{
                    font-size: 110px;
                }
        
                .join_text{
                    font-size: 43px;
                    margin-top: -15px;
                    margin-bottom: 45px;
                }
            }


            .support_help_line{
                margin-bottom: 30px;
    
                .left_contact{
                    p{
                        font-size: 16px;
                        margin-bottom: 10px;
                    }
                }
            }

            .footer_menus_and_others{
                .left_menu{
                    ul{
                        li{
                            font-size: 21px;
                            margin-bottom: 12px;
                        }
                    }
                }
    
                .others_right{
                    width: 530px;
                    gap: 18px 20px;

                    .others{
                        .head_text{
                            font-size: 16px;
                        }

                        .desc_text{
                            font-size: 13.5px;
                        }

                        a{
                            width: 30px;
                            min-width: 30px;
                            height: 30px;
                            margin-right: 10px;
                        }
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 1180px){
    .footer_containers{
        padding: 48px 24px;

        .footer_content{
            .hr_line{
                margin-bottom: 40px;
            }

            .footer_header{
                .footer_main_header{
                    font-size: 45px;
                    margin-bottom: 14px;
                }

                .know_Abt_our_client{
                    font-size: 15px;
                }
            }

            .wish_a_dish{
                padding: 30px;
                margin-bottom: 50px;

                .navigation{
                    gap: 5px;

                    .navigation_button{
                        img{
                            width: 40px;
                        }
                    }
                }
            }

            .lets_talk{
                margin-bottom: 15px;

                .left_text{
                    .big{
                        font-size: 95px;
                    }

                    .small{
                        font-size: 14px;
                    }
                }

                img{
                    width: 50px;
                }
            }

            .for_career_footer{
                padding-top: 60px;
                padding-bottom: 50px;
    
                .sounds_good_header{
                    font-size: 95px;
                }
        
                .join_text{
                    font-size: 38px;
                    margin-top: -10px;
                    margin-bottom: 40px;
                }
            }

            .support_help_line{
                margin-bottom: 40px;
    
                .left_contact{
                    p{
                        font-size: 16px;
                    }
                }
            }

            .footer_menus_and_others{
                .left_menu{
                    ul{
                        li{
                            font-size: 19px;
                            margin-bottom: 10px;
                        }
                    }
                }
    
                .others_right{
                    width: 475px;
                    gap: 18px 20px;
                    
                    .others{

                        .head_text{
                            font-size: 15px;
                            margin-bottom: 5px;
                        }

                        .desc_text{
                            font-size: 12px;
                        }

                        a{
                            width: 30px;
                            min-width: 30px;
                            height: 30px;
                            margin-right: 8px;
                        }
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 980px){
    .footer_containers{
        min-height: auto;
        padding: 40px 20px;

        .footer_content{
            .hr_line{
                margin-bottom: 35px;
            }

            .footer_header{
                .footer_main_header{
                    font-size: 40px;
                    margin-bottom: 10px;
                }

                .know_Abt_our_client{
                    font-size: 13px;
                }
            }

            .wish_a_dish{
                padding: 25px;
                margin-bottom: 40px;

                .navigation{
                    gap: 0px;
                }
            }

            .lets_talk{

                .left_text{
                    .big{
                        font-size: 85px;
                    }

                    .small{
                        font-size: 12.5px;
                    }
                }

                img{
                    width: 40px;
                }
            }

            .for_career_footer{
                padding-top: 40px;
                padding-bottom: 30px;
    
                .sounds_good_header{
                    font-size: 85px;
                }
        
                .join_text{
                    font-size: 33px;
                    margin-bottom: 35px;
                }
            }


            .support_help_line{
                margin-bottom: 40px;
    
                .left_contact{
                    p{
                        font-size: 13px;
                    }
                }
            }

            .footer_menus_and_others{
    
                .others_right{
                    .others{
                        width: 47%;

                        .head_text{
                            font-size: 14px;
                            margin-bottom: 5px;
                        }

                        .desc_text{
                            font-size: 12px;
                        }

                        a{
                            width: 25px;
                            min-width: 25px;
                            height: 25px;
                            margin-right: 8px;
                        }
                    }
                }
            }
        }
    }
}


@media only screen and (max-width: 780px){
    .footer_containers{
        min-height: auto;
        padding: 32px 15px;

        .footer_content{
            .hr_line{
                margin-bottom: 35px;
            }

            .footer_header{
                .footer_main_header{
                    font-size: 34px;
                    margin-bottom: 7px;
                }

                .know_Abt_our_client{
                    font-size: 11px;
                }
            }

            .wish_a_dish{
                padding: 20px;
                margin-bottom: 30px;

                .navigation{

                    .navigation_button{
                        img{
                            width: 35px;
                        }
                    }
                }
            }

            .lets_talk{

                .left_text{
                    .big{
                        font-size: 65px;
                    }

                    .small{
                        font-size: 10px;
                    }
                }

                img{
                    width: 30px;
                    margin: 30px 35px 0px 0px;
                }
            }

            .for_career_footer{
                padding-top: 30px;
                padding-bottom: 20px;
    
                .sounds_good_header{
                    font-size: 65px;
                }
        
                .join_text{
                    font-size: 25px;
                    margin-top: 0px;
                    margin-bottom: 30px;
                }
            }


            .support_help_line{
                margin-bottom: 30px;
    
                .left_contact{
                    p{
                        font-size: 12px;
                        margin-bottom: 7px;
                    }
                }
            }

            .footer_menus_and_others{
                .left_menu{
                    ul{
                        li{
                            font-size: 15px;
                        }
                    }
                }
    
                .others_right{
                    width: 400px;
                    gap: 12px 20px;
                    
                    .others{
                        width: 47%;

                        .head_text{
                            font-size: 12px;
                            margin-bottom: 5px;
                        }

                        .desc_text{
                            font-size: 10px;
                        }

                        a{
                            width: 20px;
                            min-width: 20px;
                            height: 20px;
                            margin-right: 8px;
                        }
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 667px){
    .footer_containers{
        &.bg_image{
            background-size:  95% auto;
            background-position: center -15vw;
        }

        .footer_content{
            .hr_line{
                margin-bottom: 5.5vw;
            }

            .footer_header{
                .footer_main_header{
                    font-size: 5.9vw;
                    margin-bottom: 7px;
                }

                .know_Abt_our_client{
                    font-size: 3vw;
                }
            }

            .wish_a_dish{
                padding: 15px;
                margin-bottom: 15vw;

                .navigation{
                    justify-content: space-between;
                    margin-top: 20px;
                    .navigation_button{
                        overflow: hidden;
                        border-radius: 4px;
                        height: 25px;
                        display: flex;
                        align-items: center;

                        img{
                            width: 15vw;
                            height: auto;
                        }
                    }
                }
            }

            .lets_talk{
                .left_text{
                    .big{
                        font-size: 9vw;
                        margin-bottom: 1vw;
                    }

                    .small{
                        font-size: 2.9vw;
                        line-height: 4vw;
                    }
                }

                img{
                    width: 4vw;
                    margin: 4vw 3vw 0px 0px;
                }
            }

            .for_career_footer{
                padding-top: 5vw;
                padding-bottom: 6vw;
    
                .sounds_good_header{
                    font-size: 9vw;
                }
        
                .join_text{
                    font-size: 4vw;
                    margin-top: 0px;
                    margin-bottom: 5vw;
                }
            }


            .support_help_line{
                margin-bottom: 5vw;
                display: block;
    
                .left_contact{
                    margin-bottom: 6vw;

                    p{
                        font-size: 3.5vw;
                        margin-bottom: 7px;
                    }
                }
            }

            .footer_menus_and_others{
                display: block;
                .left_menu{
                    margin-bottom: 10vw;

                    ul{
                        display: flex;
                        justify-content: space-around;
                        border-top: 1px solid rgba(255, 255, 255, 0.233);
                        border-bottom: 1px solid rgba(255, 255, 255, 0.233);
                        padding: 2vw 0;

                        li{
                            font-size: 4vw;
                            margin-bottom: unset;

                            &:hover{
                                padding-left: 0px;
                            }
                        }
                    }
                }
    
                .others_right{
                    width: 100%;
                    gap: 8vw;
                    
                    .others{
                        width: calc(50% - 4vw);

                        .head_text{
                            font-size: 3.8vw;
                            margin-bottom: 2vw;
                        }

                        .desc_text{
                            font-size: 3vw;
                        }

                        a{
                            width: 8vw;
                            min-width: 8vw;
                            height: 8vw;
                            margin-right: 8px;
                        }
                    }
                }
            }
        }
    }
}