@import "../../../assets/css/global.scss";

.department{
    width: 100%;
    min-height: 100vh;
    padding: 80px 40px;
    background-color: black;
    border-radius: 30px;
    padding-bottom: 160px;

    .career_card_section{
        width: 100%;
        height: auto;
        margin-bottom: 80px;
        .section{
            width: 100%;
            height: auto;
            margin-bottom: 50px;
        }
    }

    .our_department_section{
        width: 100%;
        height: auto;

        .all_department_header{
             color: white;
            font-size: 48px;
            font-family: $ubuntuMedium;
            margin-bottom: 20px;
        }

        table{
            tbody{
                tr{
                    td{
                        .arrow{
                            width: 12px;
                            height: auto;
                        }
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 1366px){
    .department{
        padding: 72px 36px;
    
        .career_card_section{
            margin-bottom: 70px;
        }
    
        .our_department_section{
            .all_department_header{
                font-size: 42px;
                margin-bottom: 17px;
            }
    
            .vacancy_table{
                table{
                    tbody{
                        tr{
                            td{
                                font-size: 21px;
                                font-family: $lexExtraLight;
                                padding: 13px 0px;
                            }
                        }
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 1180px){
    .department{
        padding: 48px 24px;
    
        .career_card_section{
            margin-bottom: 55px;
        }
    
        .our_department_section{
            .all_department_header{
                font-size: 35px;
                margin-bottom: 15px;
            }
    
            .vacancy_table{
                table{
                    tbody{
                        tr{
                            td{
                                font-size: 19px;
                                padding: 11px 0px;
                            }
                        }
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 1080px){
    .department{
        min-height: auto;
    }
}

@media only screen and (max-width: 980px){
    .department{
        padding: 40px 20px;
    
        .career_card_section{
            margin-bottom: 40px;
        }
    
        .our_department_section{
            .all_department_header{
                font-size: 26px;
                margin-bottom: 10px;
            }
            
            .sub_header{
                gap: 25px;
                margin-bottom: 30px;
                flex-wrap: wrap;
    
                p{
                    font-size: 21px;
                }
            }
    
            .vacancy_table{
                table{
                    tbody{
                        tr{
                            td{
                                font-size: 17px;
                                padding: 9px 0px;
                                
                                .arrow{
                                    width: 10px;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 780px){
    .department{
        padding: 32px 15px;
    
        .career_card_section{
            margin-bottom: 30px;
        }
    
        .our_department_section{
            .all_department_header{
                font-size: 22px;
                margin-bottom: 7px;
            }
            
            .vacancy_table{
                table{
                    tbody{
                        tr{
                            td{
                                font-size: 15px;
                                padding: 6px 0px;
                            }
                        }
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 667px){
    .department{
        padding: 32px 15px;
    
        .career_card_section{
            margin-bottom: 30px;
        }
    
        .our_department_section{
            .all_department_header{
                font-size: 6vw;
                margin-bottom: 5vw;
            }
    
            .vacancy_table{
                table{
                    tbody{
                        tr{
                            td{
                                font-size: 3vw;
                                padding: 1.5vw 0px;
                            }
                        }
                    }
                }
            }
        }
    }
}