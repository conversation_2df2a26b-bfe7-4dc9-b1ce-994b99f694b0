import PropTypes from 'prop-types';
import classes from "./styles.module.scss";
import Image from "next/image";
import Link from "next/link";

const GalaryPhoto = ({ src, second_class, link }) => (
  <div className={`${classes.galary_photo} ${second_class}`}>
    <Image src={src} width={1000} height={1000} alt="galary" className={classes.photo} />
    {link && (
      <Link href={link} className={classes.view_link}>
        View 
        <Image 
          src={`${process.env.NEXT_PUBLIC_CDN_URL}redirectArrow.webp`} 
          width={65} 
          height={65} 
          alt="" 
        />
      </Link>
    )}
  </div>
);

GalaryPhoto.propTypes = {
  src: PropTypes.string.isRequired,
  second_class: PropTypes.string,
  link: PropTypes.string,
};

GalaryPhoto.defaultProps = {
  second_class: '',
  link: null,
};

export default GalaryPhoto;
