import PropTypes from 'prop-types';
import classes from "./styles.module.scss";
import Image from "next/image";
import { motion, useTransform } from "framer-motion";
import { useRef } from "react";
import Button from "../button";
import Right from "../../assets/images/Right.webp";

const ServicesCardTwo = ({ head_title, description, tech_cards, color, progress, range, targetScale, isLastCard, right_image }) => {
    const container = useRef(null);
    const scale = useTransform(progress, range, [1, isLastCard ? 1 : targetScale]);
    const opacity = useTransform(progress, range, [1, isLastCard ? 1 : -0.2]);

    return (
        <div ref={container} className={classes.services_card_two_container}>
            <motion.div 
                style={{ scale, backgroundColor: color, opacity }} 
                className={classes.services_card_two_content}
            >
                <div className={classes.left_section}>
                    <div className={classes.left_sec_top}>
                        <p className={classes.left_section_head}>{head_title}</p>
                        <div className={classes.technology_cards_container}>{tech_cards}</div>
                        <p className={classes.desc_text}>{description}</p>
                    </div>
                    {/* <Button 
                        button_text="Find Out More" 
                        variant="button_orange"
                        icon_two={<Image src={Right} alt="arrow" />} 
                        second_class={classes.find_out_more}
                    /> */}
                </div>
                <div className={classes.right_section}>
                    {right_image}
                </div>
            </motion.div>

            <motion.div
                style={{ scale, backgroundColor: color, opacity }} 
                className={classes.services_card_two_content_tab}
            >
                <div className={classes.image_section}>
                    {right_image}
                </div>
                <p className={classes.card_head}>{head_title}</p>
                <div className={classes.tech_cards}>{tech_cards}</div>
                <p className={classes.card_desc}>{description}</p>
                {/* <Button 
                    button_text="Find Out More" 
                    variant="button_orange"
                    icon_two={<Image src={Right} alt="arrow" />} 
                    second_class={classes.find_out_more_tab}
                /> */}
            </motion.div>

            {/* <motion.div 
                style={{ scale, backgroundColor: color, opacity }} 
                className={classes.services_card_two_content_mobile}
            >
                <div className={classes.top_section_mobile}>
                    <Image className={classes.card_main_image} src={right_image} alt="im" />
                </div>

                <div className={classes.bottom_section_mobile}>
                    <p className={classes.left_section_head}>
                        {head_title}
                    </p>
                    <div className={classes.technology_cards_container}>
                        {tech_cards}
                    </div>
                    <p className={classes.desc_text}>
                        {description}
                    </p>
                    <Button 
                        button_text="Find Out More" 
                        variant="button_orange"
                        icon_two={<Image src={Right} alt="arrow" />} 
                        second_class={classes.find_out_more}
                    />
                </div>
            </motion.div> */}
        </div>
    );
};

ServicesCardTwo.propTypes = {
    head_title: PropTypes.string.isRequired,
    description: PropTypes.string.isRequired,
    color: PropTypes.string.isRequired,
    progress: PropTypes.object.isRequired,
    range: PropTypes.arrayOf(PropTypes.number).isRequired,
    targetScale: PropTypes.number.isRequired,
    tech_cards: PropTypes.array.isRequired,
    isLastCard: PropTypes.bool.isRequired,
    right_image: PropTypes.node.isRequired,
};

export default ServicesCardTwo;