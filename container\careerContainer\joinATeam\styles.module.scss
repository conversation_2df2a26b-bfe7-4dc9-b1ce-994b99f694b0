@import "../../../assets/css/global.scss";

.join_a_team{
    width: 100%;
    min-height: 100vh;
    background-color: black;
    padding: 80px 40px;

    .join_a_team_header{
        width: 100%;
        height: auto;
        display: flex;
        justify-content: space-between;
        gap: 150px;
        margin-bottom: 80px;
    
        .header_left_part{
            overflow: hidden;
            
            .head{
                font-size: 60px;
                font-family: $ubuntuRegular;
                color: white;
                margin-bottom: 30px;

                .container{
                    background-color: transparent;
                    display: inline;
                  }
                  
                  .torch_text{
                    // font-size: 4rem;s
                    font-weight: bold;
                    color: #FC9700;
                    position: relative;
                    text-align: center;
                    display: inline-block;
                    opacity: 0.2; // Initial faded state
                  
                    -webkit-mask-image: radial-gradient(
                      circle 100px at var(--x, 50%) var(--y, 50%),
                      rgba(255, 255, 255, 1),
                      rgba(255, 255, 255, 0.5),
                      rgba(255, 255, 255, 0.1)
                    );
                    mask-image: radial-gradient(
                      circle 100px at var(--x, 50%) var(--y, 50%),
                      rgba(255, 255, 255, 1),
                      rgba(255, 255, 255, 0.5),
                      rgba(255, 255, 255, 0.1)
                    );
                  
                    transition: opacity 0.4s ease-in-out, -webkit-mask-image 0.3s ease-out;
                  }
            }
    
            .desc_text, .desc_text_orange{
                font-size: 18px;
                font-family: $lexLight;
                color: white;
            }
    
            .hr{
                height: 2px;
                width: 100%;
                background-color: white;
                margin: 15px 0px;
            }
    
            .desc_text_orange{
                color: #FDB954;
                margin-bottom: 30px;
            }
    
            .see_openings{
                display: flex;
                align-items: center;
                gap: 10px;
    
                img{
                    width: 20px;
                    height: auto;
                    margin-top: 2px;
                }
            }
        }
    
        .right_banner{
            min-width: 428px;
            max-width: 428px;
            height: auto;
            aspect-ratio: 428/459;
            object-fit: cover;
            overflow: hidden;
            border-radius: 0px 50px 0px 0px;
        }
    }


    .status_of_company{
        width: 100%;
        background-color: white;
        border-radius: 16px;

        .overall_status{
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 40px;
            padding: 30px 0px;
        }

        .audit_status{
            display: flex;
            justify-content: center;
            border-top: 1px solid rgba(0, 0, 0, 0.2);

            .audit_wrapper{
                width: 200px;
                height: auto;
                border-left: 1px solid rgba(0, 0, 0, 0.2);
                display: flex;
                align-items: center;
                justify-content: center;

                &:last-child{
                    border-right: 1px solid rgba(0, 0, 0, 0.2);
                }

                img{
                    width: 100%;
                    height: auto;
                    filter: grayscale(100%);
                }

                &:hover{
                    img{
                        filter: grayscale(0%);
                    }
                }

                &:nth-child(1){
                    padding: 25px;
                }
            }
        }
    }
}

@media only screen and (max-width: 1366px){
    .join_a_team{
        padding: 72px 36px;
    
        .join_a_team_header{
            gap: 100px; 
            margin-bottom: 70px;
        
            .header_left_part{
                .head{
                    font-size: 52px;
                    margin-bottom: 25px;
                }
        
                .desc_text, .desc_text_orange{
                    font-size: 17px;
                }
        
                .see_openings{
                    gap: 8px;
        
                    img{
                        width: 18px;
                        margin-top: 3px;
                    }
                }
            }
        
            .right_banner{
                min-width: 400px;
                max-width: 400px;
                border-radius: 0px 40px 0px 0px;
            }
        }
    
    
        .status_of_company{
            .overall_status{
                gap: 35px;
                padding: 25px 0px;
            }
    
            .audit_status{
                .audit_wrapper{
                    width: 165px;
                }
            }
        }
    }
}

@media only screen and (max-width: 1280px){
    .join_a_team{
        .join_a_team_header{
            .right_banner{
                min-width: 370px;
                max-width: 370px;
                border-radius: 0px 40px 0px 0px;
            }
        }
    }
}

@media only screen and (max-width: 1180px){
    .join_a_team{
        padding: 48px 24px;
    
        .join_a_team_header{
            gap: 80px; 
            margin-bottom: 60px;
        
            .header_left_part{
                .head{
                    font-size: 45px;
                    margin-bottom: 20px;
                }
        
                .desc_text, .desc_text_orange{
                    font-size: 16px;
                }
        
                .see_openings{
                    img{
                        width: 16px;
                    }
                }
            }
        
            .right_banner{
                min-width: 350px;
                max-width: 350px;
                border-radius: 0px 40px 0px 0px;
            }
        }
    
    
        .status_of_company{
            .overall_status{
                gap: 30px;
                padding: 20px 0px;
            }
    
            .audit_status{
                .audit_wrapper{
                    width: 145px;
                }
            }
        }
    }
}

@media only screen and (max-width: 1080px){
    .join_a_team{
        min-height: auto;

        .join_a_team_header{
            .right_banner{
                min-width: 330px;
                max-width: 330px;
                border-radius: 0px 40px 0px 0px;
            }
        }
    }
}

@media only screen and (max-width: 980px){
    .join_a_team{
        padding: 40px 20px;
    
        .join_a_team_header{
            gap: 80px; 
            margin-bottom: 60px;
        
            .header_left_part{
                .head{
                    font-size: 40px;
                    margin-bottom: 15px;
                }
        
                .desc_text, .desc_text_orange{
                    font-size: 15px;
                }
        
                .hr{
                    height: 1px;
                    margin: 10px 0px;
                }
            }
        
            .right_banner{
                min-width: 300px;
                max-width: 300px;
                border-radius: 0px 40px 0px 0px;
            }
        }
    
    
        .status_of_company{
            border-radius: 12px;
    
            .overall_status{
                gap: 25px;
                padding: 15px 0px;
            }
    
            .audit_status{

                .audit_wrapper{
                    width: 130px;

                    img{
                        filter: grayscale(0%);
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 880px){
    .join_a_team{
        .join_a_team_header{
            .right_banner{
                min-width: 260px;
                max-width: 260px;
                border-radius: 0px 40px 0px 0px;
            }
        }
    }
}

@media only screen and (max-width: 780px){
    .join_a_team{
        padding: 32px 16px;
    
        .join_a_team_header{
            gap: 50px; 
            margin-bottom: 60px;
        
            .header_left_part{
                .head{
                    font-size: 34px;
                    margin-bottom: 10px;
                }
        
                .desc_text, .desc_text_orange{
                    font-size: 14px;
                }
        
                .hr{
                    margin: 7px 0px;
                }
        
                .see_openings{
                    gap: 5px;
        
                    img{
                        width: 13px;
                        margin-top: 2px;
                    }
                }
            }
        
            .right_banner{
                min-width: 220px;
                max-width: 220px;
                border-radius: 0px 40px 0px 0px;
            }
        }
    
    
        .status_of_company{
            .overall_status{
                gap: 20px;
                padding: 10px 0px;
            }
    
            .audit_status{

                .audit_wrapper{
                    width: 110px;
                }
            }
        }
    }
}

@media only screen and (max-width: 667px){
    .join_a_team{
        padding: 32px 15px;

        .join_a_team_header{
            flex-direction: column-reverse;
            gap: 5vw;
            margin-bottom: 8vw;

            .header_left_part{
                .head{
                    font-size: 8.2vw;
                    margin-bottom: 4vw;

                    br{
                        display: none;
                    }

                    span{
                        opacity: 1;
                    }

                    .container{
                        .torch_text{
                            opacity: 1;
                        }
                    }
                }

                .desc_text, .desc_text_orange{
                    font-size: 3.4vw;
                    line-height: 5vw;
                }

                .hr{
                    height: 2px;
                    margin: 3vw 0px;
                }

                .desc_text_orange{
                    margin-bottom: 5vw;
                }

                .see_openings{
                    gap: 1.5vw;
        
                    img{
                        width: 3.5vw;
                    }
                }
            }
            
            .right_banner{
                min-width: 100%;
                max-width: 100%;
                height: auto;
                aspect-ratio: 428/250;
                object-fit: cover;
                overflow: hidden;
                border-radius: 0px 10vw 0px 0px;
            }
        }

        .status_of_company{
            border-radius: 3vw;
            overflow: hidden;
    
            .overall_status{
                flex-wrap: wrap;
                gap: 10px;
                padding: 10px 0px 10px;
            }
    
            .audit_status{
                .audit_wrapper{
                    width: 23%;

                    &:nth-child(1){
                        padding: 10px;
                    }
                }
            }
        }
    }
}