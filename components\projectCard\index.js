import PropTypes from 'prop-types';
import classes from "./styles.module.scss";
import Image from "next/image";

const ProjectCard = (props) => {
    return (
        <div className={classes.project_card}>
            <Image className={classes.card_image} src={props.project_img} alt="project" width={874} height={526} />
            <div className={classes.card_header}>
                <p>{props.head_text}</p>
                {/* <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}redirectArrow.webp`} width={65} height={65} alt="arrow" /> */}
            </div>
            <p className={classes.desc_text}>
                {props.desc_text}
            </p>
        </div>
    );
};

ProjectCard.propTypes = {
    head_text: PropTypes.string.isRequired,
    desc_text: PropTypes.node.isRequired,
    project_img: PropTypes.string.isRequired,
};

export default ProjectCard;