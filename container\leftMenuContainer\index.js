import classes from "./styles.module.scss";
import Image from "next/image";
import { useRouter } from 'next/router';
import Button from "@/components/button";
import PropTypes from "prop-types";
import Link from "next/link";

const LeftMenuContainer = ({ onClose }) =>{

    const router = useRouter();

    const handleNavigation = (path) => {
        router.push(path);
        if (onClose) onClose();
    };

    return(
        <div className={classes.left_menu_container}>
            <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}servicesBgEllipseTwo.webp`} width={800} height={800} alt="" className={classes.eclipse_one} />
            <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}servicesBgEllipseOne.webp`} width={800} height={800} alt="" className={classes.eclipse_two} />

            <div className={classes.left_menu_content}>
                <Button 
                    onClick={onClose} 
                    second_class={classes.close} 
                    icon={<Image src={`${process.env.NEXT_PUBLIC_CDN_URL}CloseIconBlack.webp`} width={172} height={172} alt='icon' />} 
                />

                <div className={classes.left_content}>
                    <ul>
                        
                        <li className={classes.menu_line}>
                            <button onClick={() => handleNavigation("/")}>
                                <div className={classes.animation_container}>
                                    <div className={classes.animation_div} style={{animationDuration: "2s"}}>
                                        <p className={classes.p_}>Home</p>
                                        <p className={classes.p}>Home</p>                        
                                    </div>
                                </div>
                                <Image className={classes.arrow} src={`${process.env.NEXT_PUBLIC_CDN_URL}redirectArrow.webp`} width={65} height={65} alt="icon" />
                            </button>
                        </li>

                        <li className={classes.menu_line}>
                            <button onClick={() => handleNavigation('/product')}>
                                <div className={classes.animation_container}>
                                    <div className={classes.animation_div} style={{animationDuration: "1.5s"}}>
                                        <p className={classes.p_}>Products</p>
                                        <p className={classes.p}>Products</p>                        
                                    </div>
                                </div>
                                <Image className={classes.arrow} src={`${process.env.NEXT_PUBLIC_CDN_URL}redirectArrow.webp`} width={65} height={65} alt="icon" />
                            </button>
                        </li>
                        <li className={classes.menu_line}>
                            <button onClick={() => handleNavigation('https://codebucketlab.com/')}>
                                <div className={classes.animation_container}>
                                    <div className={classes.animation_div} style={{animationDuration: "2s"}}>
                                        <p className={classes.p_}>cB Lab</p>
                                        <p className={classes.p}>cB Lab</p>                        
                                    </div>
                                </div>
                                <Image className={classes.arrow} src={`${process.env.NEXT_PUBLIC_CDN_URL}redirectArrow.webp`} width={65} height={65} alt="icon" />
                            </button>
                        </li>
                        <li className={classes.menu_line}>
                            <button onClick={() => handleNavigation("/career")}>
                                <div className={classes.animation_container}>
                                    <div className={classes.animation_div} style={{animationDuration: "2.5s"}}>
                                        <p className={classes.p_}>Careers</p>
                                        <p className={classes.p}>Careers</p>                        
                                    </div>
                                </div>
                                <Image className={classes.arrow} src={`${process.env.NEXT_PUBLIC_CDN_URL}redirectArrow.webp`} width={65} height={65} alt="icon" />
                            </button>
                        </li>
                        <li className={classes.menu_line}>
                            <button onClick={() => handleNavigation("/contact-us")}>
                                <div className={classes.animation_container}>
                                    <div className={classes.animation_div} style={{animationDuration: "3s"}}>
                                        <p className={classes.p_}>Contact Us</p>
                                        <p className={classes.p}>Contact Us</p>                        
                                    </div>
                                </div>
                                <Image className={classes.arrow} src={`${process.env.NEXT_PUBLIC_CDN_URL}redirectArrow.webp`} width={65} height={65} alt="icon" />
                            </button>
                        </li>
                    </ul>
                </div>
                <div className={classes.right_content}>
                    <Image className={classes.company_logo} src={`${process.env.NEXT_PUBLIC_CDN_URL}cbWhite.webp`} width={100} height={100} alt="logo" />
                    <p className={classes.mail}><EMAIL></p>
                    <p className={classes.phone}>+91 99950 08671</p>

                    <div className={classes.head_desc}>
                        <p className={classes.head}>Registered Office:</p>
                        <p className={classes.desc}>HMT Colony,Kalamassery, Kochi, Kerala.</p>
                    </div>
                    
                    <div className={classes.head_desc}>
                        <p className={classes.desc}>
                            WeWork Prestige Central, 36, <br /> 
                            Infantry Road, Bangalore, <br />
                            Karnantaka 560001
                        </p>
                    </div>

                    <div className={classes.head_desc}>
                        <p className={classes.desc}>
                            Opposite Gate No 93, Digha <br /> 
                            Danapur Road, Patna - 800013
                        </p>
                    </div>
                    <div className={classes.head_desc}>
                        <p className={classes.head}>Connect with Us</p>
                        <div className={classes.social_icons}>
                            <Link href={"https://www.facebook.com/codebuckets"} target='_blank'><Image className={classes.icons} src={`${process.env.NEXT_PUBLIC_CDN_URL}Facebook.webp`} width={40} height={40} alt="icon" /></Link>
                            <Link href={"https://www.instagram.com/codebuckets_/"} target='_blank'><Image className={classes.icons} src={`${process.env.NEXT_PUBLIC_CDN_URL}Instagram.webp`} width={40} height={40} alt="icon" /></Link>
                            <Link href={"https://x.com/codebuckets_"} target='_blank'><Image className={classes.icons} src={`${process.env.NEXT_PUBLIC_CDN_URL}Twitter.webp`} width={40} height={40} alt="icon" /></Link>
                            <Link href={'https://www.linkedin.com/company/13441306'} target='_blank'><Image className={classes.icons} src={`${process.env.NEXT_PUBLIC_CDN_URL}LinkedIn.webp`} width={40} height={40} alt="icon" /></Link>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

LeftMenuContainer.propTypes = {
    onClose: PropTypes.func,
};

export default LeftMenuContainer;