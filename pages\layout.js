import { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import classes from "./styles.module.scss";
import Header from "../container/header";
import Footer from '../container/footer';
import Lenis from "@studio-freight/lenis";

const Layout = (props) => {
    const [showMenus, setShowMenus] = useState(false);

    useEffect(() => {
        if (!showMenus) {
            const lenis = new Lenis();

            function raf(time) {
                lenis.raf(time);
                requestAnimationFrame(raf);
            }

            requestAnimationFrame(raf);

            return () => {
                lenis.destroy();
            };
        }
    }, [showMenus]);

    return (
        <div className={classes.main_page_container} id="mainBody">
            <div className={classes.header_section}>
                <Header 
                    showMenus={showMenus} 
                    setShowMenus={setShowMenus} 
                /> 
            </div>
            <div className={classes.body_section}>
                {props.children}
            </div>
            <div className={classes.footer_Setion}>
                <Footer />
            </div>
        </div>
    );
};

Layout.propTypes = {
    children: PropTypes.node,
};

export default Layout;