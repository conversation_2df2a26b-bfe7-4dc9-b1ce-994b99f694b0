import classes from "./styles.module.scss"; 
import { motion, useAnimation } from "framer-motion";
import { useInView } from "react-intersection-observer";
import React from "react";

const WhyChooseUs = () => {
    const controls = useAnimation();
    const [ref1, inView1] = useInView({ threshold: 0.5 });
    const [ref2, inView2] = useInView({ threshold: 0.1 });
    const [ref3, inView3] = useInView({ threshold: 0.5 });

    React.useEffect(() => {
        if (inView1) {
            controls.start("slideOne");
        } else if (inView2) {
            controls.start("slideTwo");
        } else if (inView3) {
            controls.start("slideThree");
        } else {
            controls.start("slideOne");
        }
    }, [inView1, inView2, inView3, controls]);

    console.log(inView1, inView2, inView3);

    const renderContent = () => {
        if (inView1) {
            return (
                <div className={classes.animation_content}>
                    <div className={classes.center_content}>
                        <div className={classes.left_content}>
                            <motion.p 
                                className={classes.left_content_header}
                            >
                                Commitment <br /> 
                                to Quality and <br /> 
                                Performance
                            </motion.p>
                        </div>
                        <div className={classes.right_content}>
                            <motion.div
                            >
                                <div className={classes.custom_list}>
                                    <p className={classes.numbering}>01</p>
                                    <p className={classes.text}>We use cutting-edge technologies and innovative tactics to help businesses <span className={classes.green}>solve problems</span>, raise opportunities, and <span className={classes.orange}>achieve</span> their objectives more quickly.</p>
                                </div>
                                <div className={classes.custom_list}>
                                    <p className={classes.numbering}>02</p>
                                    <p className={classes.text}>By optimizing <span className={classes.orange}>strategies</span> and <span className={classes.orange}>allocating resources efficiently</span>, we make sure you get the most out of any investment.</p>
                                </div>
                                <div className={classes.custom_list}>
                                    <p className={classes.numbering}>03</p>
                                    <p className={classes.text}>Our <span className={classes.green}>Dedication</span> to quality ensures that we give superior results to our valuable Clients.</p>
                                </div>
                            </motion.div>
                        </div>
                    </div>
                    <div className={classes.status_bar}>
                        <div className={classes.bar}></div>
                        <div className={classes.status}>
                            <div className={`${classes.indicator} ${classes.active}`}>
                                <p>01</p>
                            </div>
                            <div className={`${classes.indicator} ${classes.active}`}>
                                <p>02</p>
                            </div>
                            <div className={`${classes.indicator}`}>
                                <p>03</p>
                            </div>
                        </div>
                    </div>
                </div>
            );
        } else if (inView2) {
            return (
                <div className={classes.animation_content}>
                    <div className={classes.center_content}>
                        <div className={classes.left_content}>
                            <motion.p
                                className={classes.left_content_header}
                            >
                                Safeguarding <br /> 
                                Your Digital <br />
                                World
                            </motion.p>
                        </div>
                        <div className={classes.right_content}>
                            <motion.div
                            >
                                <div className={classes.custom_list}>
                                    <p className={classes.numbering}>01</p>
                                    <p className={classes.text}>Your data and assets are <span className={classes.orange}>protected</span> against cyber threats using <span className={classes.green}>top-tier cybersecurity solutions</span>, securing your organization in the digital world.</p>
                                </div>
                                <div className={classes.custom_list}>
                                    <p className={classes.numbering}>02</p>
                                    <p className={classes.text}>Helping your company navigate the digital frontier with confidence implies that your digital assets and data are <span className={classes.green}>safe from threats and harm</span>.</p>
                                </div>
                                <div className={classes.custom_list}>
                                    <p className={classes.numbering}>03</p>
                                    <p className={classes.text}>We continuously analyze and <span className={classes.orange}>improve your cybersecurity</span> posture as new threats and vulnerabilities emerge, ensuring that your digital assets are <span className={classes.green}>safeguarded</span>.</p>
                                </div>
                            </motion.div>
                        </div>
                    </div>
                    <div className={classes.status_bar}>
                        <div className={classes.bar}></div>
                        <div className={classes.status}>
                            <div className={`${classes.indicator} ${classes.active}`}>
                                <p>01</p>
                            </div>
                            <div className={`${classes.indicator} ${classes.active}`}>
                                <p>02</p>
                            </div>
                            <div className={`${classes.indicator} ${classes.active}`}>
                                <p>03</p>
                            </div>
                        </div>
                    </div>
                </div>
            );
        } else {
            return (
                <div className={classes.animation_content}>
                    <div className={classes.content_header}>
                        <p className={classes.header}>Why Choose Us?</p>
                        <p className={classes.desc_text}>We offer a team of seasoned professionals with years of </p>
                    </div>
                    <div className={classes.center_content}>
                        <div className={classes.left_content}>
                            <motion.p 
                                className={classes.left_content_header}
                            >
                                Unmatched <br /> 
                                Expertise
                            </motion.p>
                        </div>
                        <div className={classes.right_content}>
                            <motion.div
                            >
                                <div className={classes.custom_list}>
                                    <p className={classes.numbering}>01</p>
                                    <p className={classes.text}>Our unmatched expertise in <span className={classes.green}>IT services</span> makes us <span className={classes.orange}>stand out</span> in the crowd.</p>
                                </div>
                                <div className={classes.custom_list}>
                                    <p className={classes.numbering}>02</p>
                                    <p className={classes.text}>We offer a team of seasoned professionals with years of industry experience who provide us with <span className={classes.green}>customized solutions</span> that suit individual client requirements.</p>
                                </div>
                                <div className={classes.custom_list}>
                                    <p className={classes.numbering}>03</p>
                                    <p className={classes.text}>Our proactive and strategic approach ensures that we <span className={classes.green}>anticipate challenges</span>.</p>
                                </div>
                                <div className={classes.custom_list}>
                                    <p className={classes.numbering}>04</p>
                                    <p className={classes.text}><span className={classes.orange}>Deliver solutions</span> that keep your business ahead of the competition.</p>
                                </div>
                            </motion.div>
                        </div>
                    </div>
                    <div className={classes.status_bar}>
                        <div className={classes.bar}></div>
                        <div className={classes.status}>
                            <div className={`${classes.indicator} ${classes.active}`}>
                                <p>01</p>
                            </div>
                            <div className={`${classes.indicator}`}>
                                <p>02</p>
                            </div>
                            <div className={classes.indicator}>
                                <p>03</p>
                            </div>
                        </div>
                    </div>
                </div>
            );
        }
    };

    return (
        <div className={classes.why_choose_us_container}>
            <div className={classes.why_choose_us_content} style={{ height: "300vh" }}>
                <div className={classes.content}>
                    <div className={classes.framer_wrapper}>
                        {renderContent()}
                    </div>
                </div>

                <div ref={ref1} style={{ height: "100vh"}}></div>
                <div ref={ref2} style={{ height: "100vh"}}></div>
                <div ref={ref3} style={{ height: "100vh"}}></div>
            </div>
        </div>
    );
};

export default WhyChooseUs;