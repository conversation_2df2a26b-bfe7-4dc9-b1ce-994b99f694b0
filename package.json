{"name": "my-next-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:stage": "next build", "build:master": "env-cmd -f .env.master next build", "start:master": "env-cmd -f .env.master next start", "dev:master": "env-cmd -f .env.master next dev", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.4.6", "@mui/material": "^6.4.6", "@reduxjs/toolkit": "^2.2.7", "@studio-freight/lenis": "^1.0.42", "caniuse-lite": "^1.0.30001699", "framer-motion": "^11.5.4", "google-maps-react": "^2.0.6", "gsap": "^3.12.7", "next": "14.2.5", "react": "^18", "react-awesome-reveal": "^4.3.1", "react-countup": "^6.5.3", "react-dom": "^18", "react-intersection-observer": "^9.13.0", "react-redux": "^9.1.2", "react-reveal": "^1.2.2", "react-simple-maps": "^3.0.0", "sass": "^1.77.8", "sharp": "^0.33.4", "swiper": "^11.1.9"}, "devDependencies": {"eslint": "^8", "eslint-config-next": "14.2.5"}}