import { useRef, useEffect, useState } from "react";
import classes from "./styles.module.scss";
import UnlockGrowth from "./unlockGrowth";
import DevelopmentDesignAnalyticsTwo from "./developmentDesignAnalyticsTwo";
import SatisfiedResult from "./satisfiedResult";
import OurProjects from "./ourProjects";

const HomeContainer = () => {

    const unlockGrowthRef = useRef(null);
    const developmentDesignAnalyticsTwoRef = useRef(null);
    const satisfiedResultRef = useRef(null);
    const ourProjectsRef = useRef(null);

    const [heightDifferences, setHeightDifferences] = useState({
        unlockGrowth: 0,
        developmentDesignAnalyticsTwo: 0,
        satisfiedResult: 0,
        ourProjects: 0,
    });

    useEffect(() => {
        const handleResize = () => {
            const viewportHeight = window.innerHeight;
            setHeightDifferences({
                unlockGrowth: unlockGrowthRef.current ? unlockGrowthRef.current.clientHeight - viewportHeight : 0,
                developmentDesignAnalyticsTwo: developmentDesignAnalyticsTwoRef.current ? developmentDesignAnalyticsTwoRef.current.clientHeight - viewportHeight : 0,
                satisfiedResult: satisfiedResultRef.current ? satisfiedResultRef.current.clientHeight - viewportHeight : 0,
                ourProjects: ourProjectsRef.current ? ourProjectsRef.current.clientHeight - viewportHeight : 0,
            });
        };

        handleResize(); // Initial calculation
        window.addEventListener('resize', handleResize);

        // Cleanup on unmount
        return () => {
            window.removeEventListener('resize', handleResize);
        };
    }, []); // Only run once on mount

    return (
        <div className={classes.home_container}>
            <div className={classes.home_content}>
                <div
                    className={classes.view_content}
                    ref={unlockGrowthRef}
                    style={{
                        top: `-${heightDifferences.unlockGrowth}px`,
                    }}
                >
                    <UnlockGrowth />
                </div>
                <div
                    className={classes.view_content}
                    ref={developmentDesignAnalyticsTwoRef}
                    style={{
                        top: `-${heightDifferences.developmentDesignAnalyticsTwo}px`, 
                    }}
                >
                    <DevelopmentDesignAnalyticsTwo />
                </div>
                <div
                    className={classes.view_content}
                    ref={satisfiedResultRef}
                    style={{
                        top: `-${heightDifferences.satisfiedResult}px`,
                    }}
                >
                    <SatisfiedResult />
                </div>
                <div
                    className={classes.view_content}
                    ref={ourProjectsRef}
                    style={{
                        top: `-${heightDifferences.ourProjects}px`,
                    }}
                >
                    <OurProjects />
                </div>
            </div>
        </div>
    );
}

export default HomeContainer;