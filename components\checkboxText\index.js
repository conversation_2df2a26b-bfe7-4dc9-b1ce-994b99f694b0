import React, { useState } from 'react';
import PropTypes from 'prop-types';
import classes from './styles.module.scss';

const CheckboxText = ({ label, initialChecked, sec_class }) => {
    const [checked, setChecked] = useState(initialChecked);

    const handleChange = () => {
        setChecked(!checked);
    };

    return (
        <label className={`${classes.custom_checkbox} ${sec_class}`}>
            <input
                type="checkbox"
                checked={checked}
                onChange={handleChange}
            />
            {label}
        </label>
    );
};

CheckboxText.propTypes = {
    label: PropTypes.string.isRequired,
    initialChecked: PropTypes.bool,
    sec_class: PropTypes.string
};

CheckboxText.defaultProps = {
    initialChecked: false,
    sec_class: ''
};

export default CheckboxText;