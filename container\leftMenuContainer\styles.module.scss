@import "../../assets/css/global.scss";

.left_menu_container{
    width: 100%;
    height: 100vh;
    background: #020202b6;
    backdrop-filter: blur(2px);
    position: relative;
    overflow: hidden;

    .eclipse_one{
        position: absolute;
        right: 0;
        width: 40vw;
        height: 40vw;
        filter: blur(70px);
        opacity: 0.6;
    }

    .eclipse_two{
        position: absolute;
        left: 0;
        bottom: 3vw;
        width: 25vw;
        height: 25vw;
        filter: blur(100px);
        opacity: 0.8;
    }

    .left_menu_content{
        width: 100%;
        height: 100%;
        padding: 20px 40px;
        display: flex;
        align-items: flex-start;
        justify-content: flex-start;
        position: relative;
        gap: 3vw;

        .close{
            position: absolute;
            top: -10px;
            right: 20px;
            margin: 0;
            padding: 0;

            img{
                width: 5vw;
                height: auto;
            }
        }

        .left_content{
            width: 65%;

            ul{
                margin: 0;
                padding: 0;
                list-style-type: none;
                width: 100%;

                .menu_line{
                    border-bottom: 2px solid white;
                    padding: 1.1vw;
                    cursor: pointer;

                    button{
                        width: 100%;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        border: none;
                        background-color: transparent;

                        .animation_container{
                            width: auto;
                            overflow: hidden;
                            cursor: pointer;
                            height: 6.2vw;
    
                            .animation_div{
                                width: auto;
                                animation-name: example;
                                animation-timing-function: linear;
                                animation-iteration-count: infinite;
                                animation-direction: alternate;
                                animation-direction: both;
                                cursor: pointer;

    
                                .p, .p_{
                                    outline: none;
                                    font-size: 4.8vw;
                                    font-family: $ubuntuLight;
                                    cursor: pointer;
                                    color: white;
                                }
            
                                .p_{
                                    transform: rotate(-180deg);
                                }
                            }
    
                            @keyframes example{
                                0% {transform: translateY(-50%);}
                                10% {transform: translateY(-50%);}
                                20% {transform: translateY(-50%);}
                                30% {transform: translateY(-50%);}
                                40% {transform: translateY(-50%);}
                                50% {transform: translateY(0%);}
                                60% {transform: translateY(0%);}
                                70% {transform: translateY(0%);}
                                80% {transform: translateY(0%);}
                                90% { transform: translateY(0%);}
                                100% {transform: translateY(0%);}
                            }
                        }

                        .arrow{
                            width: 2vw;
                            height: 2vw;
                        }
                    }
                }
            }
        }

        .right_content{
            padding-top: 3vw;

            .company_logo{
                width: 6vw;
                height: auto;
                margin-bottom: 0.5vw;
            }

            .mail, .phone{
                font-size: 1.4vw;
                font-family: $lexLight;
                font-style: italic;
                color: white;
                margin-bottom: 0.5vw;
            }

            .phone{
                margin-bottom: 10vw;
            }


            .head_desc{
                margin-bottom: 1.5vw;

                .head{
                    font-size: 1.3vw;
                    font-family: $lexRegular;
                    margin-bottom: 1vw;
                    color: white;
                    letter-spacing: 0.5px;
                }

                .desc{
                    font-size: 1.1vw;
                    font-family: $lexLight;
                    color: white;
                    letter-spacing: 1px;
                }

                .social_icons{
                    display: flex;
                    align-items: center;
                    gap: 0.6vw;

                    img{
                        width: 1.5vw;
                        height: auto;
                        margin-bottom: 5px;
                    }
                }
            }

            .bottom{
                display: flex;
                align-items: flex-end;
                gap: 50px;
            }
        }
    }
}

@media only screen and (min-width: 2000px){
    .left_menu_container{
        .left_menu_content{
            .close{
                img{
                    width: 140px;
                }
            }

            .left_content{
                width: 1200px;

                ul{
                    .menu_line{
                        margin-bottom: 10px;
                        padding: 25px;

                        button{
                            .animation_container{
                                height: 124px;

                                .animation_div{
                                    .p, .p_{
                                        font-size: 99px;
                                    }
                                }
                            }

                            .arrow{
                                width: 40px;
                                height: 40px;
                            }
                        }
                    }
                }
            }

            .right_content{
                padding-top: 50px;

                .company_logo{
                    width: 120px;
                    height: auto;
                    margin-bottom: 20px;
                }

                .mail, .phone{
                    font-size: 30px;
                    margin-bottom: 10px;
                }

                .phone{
                    margin-bottom: 215px;
                }

                .head_desc{
                    margin-bottom: 30px;

                    .head{
                        font-size: 28px;
                        margin-bottom: 30px;
                    }

                    .desc{
                        font-size: 26px;
                    }

                    .social_icons{
                        img{
                            width: 40px;
                            height: auto;
                        }
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 1366px){
    .left_menu_container{
        .left_menu_content{
            .close{
                top: -5px;
            }
        }
    }
}
@media only screen and (max-width: 1180px){
    .left_menu_container{
        .left_menu_content{
            .close{
                top: 0px;
                right: 15px;
            }
        }
    }
}

@media only screen and (max-width: 1024px){
    .left_menu_container{
        .left_menu_content{
            .close{
                top: 17px;
                right: 35px;
            }
        }
    }
}

@media only screen and (max-width: 667px){
    .left_menu_container{
        padding: 20px 15px;

        .left_menu_content{
            flex-direction: column;

            .close{
                right: -13px;
                top: -24px;

                img{
                    width: 16vw;
                }
            }

            .left_content{
                width: 100%;
                margin-top: 20vw;

                ul{
                    .menu_line{
                        border-bottom: unset;
                        padding: 2vw;

                        button{
                            .animation_container{
                                width: 100%;
                                height: 10.5vw;
                                
                                .animation_div{
                                    .p, .p_{
                                        font-size: 8vw;
                                    }
                                }
                            }

                            .arrow{
                                display: none;
                            }
                        }
                    }
                }
            }

            .right_content{
                margin-top: 4vw;
                width: 100%;
                display: none;

                .company_logo{
                    width: 15vw;
                    position: relative;
                    left: 50%;
                    transform: translate(-50%);
                }
            }
        }
    }
}