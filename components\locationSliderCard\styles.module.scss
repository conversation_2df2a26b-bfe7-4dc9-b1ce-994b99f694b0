@import "../../assets/css/global.scss";

.location_slider_card {
    height: 95vh;
    width: 150px;
    display: flex;
    border: unset;
    outline: unset;
    background-color: black;
    transition: 300ms;

    .left_name {
        width: 150px;
        height: 100%;
        background-color: #252525;
        display: flex;
        justify-content: center;
        overflow: hidden;
        align-items: center;
        cursor: pointer;
        transition: 300ms;

        .name {
            text-align: center;
            color: rgba(255, 255, 255, 0.5);
            font-family: $ubuntuLight;
            font-size: 62px;
            transform: rotate(-90deg);
            user-select: none;
            transition: 300ms;
        }
    }

    .right_map {
        width: 0px;
        height: 100%;
        position: relative;
        overflow: hidden;

        .map {
            width: 100%;
            height: 100%;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }

        .address_details {
            width: 400px;
            min-height: 200px;
            position: absolute;
            right: 0;
            top: 0;
            background-image: linear-gradient(to right, black 50%, transparent 50%);
            overflow: hidden;
            // display: flex;
            // justify-content: center;
            // align-items: start;
            // flex-direction: column;
            padding: 30px;
            transition: 300ms;

            .text {
                color: rgba(255, 255, 255, 0.8);
                font-size: 18px;
                font-family: $lexRegular;
                margin-bottom: 15px;
                text-align: left;

                &:last-child{
                    margin-bottom: unset;
                }
            }
        }
    }

    &.expanded {
        width: 100%;

        .left_name {
            width: 300px;
            min-width: 300px;
            background-color: #890000;

            .name {
                font-size: 80px;
                color: white;
                font-family: $ubuntuBold;
            }
        }

        .right_map {
            width: calc(100% - 150px);
        }
    }
}

@media only screen and (max-width: 1366px){
    .location_slider_card {
        width: 130px;
    
        .left_name {
            width: 130px;
    
            .name {
                font-size: 42px;
            }
        }
    
        .right_map {
            .address_details {
                width: 337px;
                min-height: 180px;
                padding: 30px;
    
                .text {
                    font-size: 15px;
                    // margin-bottom: 15px;
                }
            }
        }
    
        &.expanded {
            width: 100%;
    
            .left_name {
                width: 260px;
                min-width: 260px;
    
                .name {
                    font-size: 76px;
                }
            }
    
            .right_map {
                width: calc(100% - 130px);
            }
        }
    }
}

@media only screen and (max-width: 667px){
    .location_slider_card {
        height: auto;
        width: 100%;
        display: block;
    
        .left_name {
            width: 100%;
            height: auto;
            padding: 4vw;
    
            .name {
                text-align: center;
                font-size: 5vw;
                transform: unset;
            }
        }
    
        .right_map {
            width: 100%;
            height: 0;
            transition: 300ms ease-in-out;
    
            .address_details {
                width: 50%;
                min-height: 130px;
                padding: 3vw;
    
                .text {
                    font-size: 2.7vw;
                    margin-bottom: 1vw;
    
                    &:last-child{
                        margin-bottom: unset;
                    }
                }
            }
        }
    
        &.expanded {
            width: 100%;
    
            .left_name {
                width: 100%;
    
                .name {
                    font-size: 8vw;
                    font-family: $ubuntuRegular;
                }
            }
    
            .right_map {
                width: 100%;
                height: 70vw;
                aspect-ratio: 1/1;
            }
        }
    }
}