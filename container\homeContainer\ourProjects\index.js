import { useRef, useState } from 'react';
import classes from "./styles.module.scss";
import Button from "../../../components/button";
import Image from "next/image";
import ProjectCard from "../../../components/projectCard";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/free-mode';
import 'swiper/css/pagination';

const OurProjects = () => {

    const swiperRef = useRef(null);

    const handlePrev = () => {
        if (swiperRef.current) {
            swiperRef.current.slidePrev();
        }
    };
 
    const handleNext = () => {
        if (swiperRef.current) {
            swiperRef.current.slideNext();
        }
    };

    const [activeDiv, setActiveDiv] = useState('div2');

    const handleButtonClickTwo = (divId) => {
        setActiveDiv(divId);
    };


    return(
            <div className={classes.our_projects}>
                <div className={classes.first_slider_content}>

                    <div className={classes.left_content}>
                            <p className={classes.left_head}>
                                Projects From [cB]
                            </p>
                            <p className={classes.sub_text}>
                                We take immense pride in crafting exceptional software solutions 
                                that deliver real results. Here, you can explore a curated selection of our best work
                            </p>

                            {/* <Button 
                                button_text="View More"
                                variant="button_orange"
                            /> */}

                        <div className={classes.right_swiper_navigation_button}>
                                <Button 
                                    icon={<Image src={`${process.env.NEXT_PUBLIC_CDN_URL}whiteLeft.webp`} width={32} height={28} alt="icon" />}
                                    onClick={handlePrev}
                                />
                                <Button 
                                    icon={<Image src={`${process.env.NEXT_PUBLIC_CDN_URL}whiteRight.webp`} width={32} height={28} alt="icon" />}
                                    onClick={handleNext}
                                />
                        </div>
                    </div>

                    <div className={classes.right_slider_container}>
                        <div className={classes.right_slider_content}>
                            <Swiper
                                onSwiper={(swiper) => {
                                    swiperRef.current = swiper;
                                }}
                                slidesPerView={2}
                                spaceBetween={30}
                                freeMode={true}
                                pagination={{
                                    clickable: true,
                                    renderBullet: (index, className) => `
                                        <span class="${className}" 
                                            style="
                                                background-color: ${className.includes('swiper-pagination-bullet-active') ? '#ff5733' : '#ccc'}; 
                                                width: 10px;
                                                height: 10px;
                                                margin: 0 5px;
                                                border-radius: 50%;
                                            ">
                                        </span>
                                    `,
                                }}
                                loop={true}
                                modules={[Pagination]}

                                breakpoints={{
                                    300: {
                                        slidesPerView: 1,
                                        spaceBetween: 20,
                                    },
                                    668: {
                                        slidesPerView: 2,
                                        spaceBetween: 30,
                                    }
                                }}
                                className={classes.custom_swiper} 
                            >
                                <SwiperSlide>
                                    <ProjectCard 
                                        head_text="THE CREATORS UNITED"
                                        // project_img={CU}
                                        project_img={`${process.env.NEXT_PUBLIC_CDN_URL}CU.webp`}
                                        desc_text={ 
                                            <>
                                                Creators United is an India-based influencer-focused lifestyle entertainment platform that provides the latest news, trends, and insights in the influencer marketing 
                                                industry. It is a comprehensive resource for influencers and content creators to stay updated on industry developments. Creators United connects creators, brands, and 
                                                audiences in a dynamic digital space. 
                                                {/* From concept to execution, we played a crucial role in designing and developing this cutting-edge platform. Our team focused on building a seamless, responsive, 
                                                user-friendly website that enhances engagement while ensuring a smooth browsing experience. The platform features custom UI/UX design, high-performance architecture, 
                                                and robust security, making it a reliable hub for influencers and industry professionals. */}
                                            </>
                                        }
                                    />
                                </SwiperSlide>
                                
                                <SwiperSlide>
                                    <ProjectCard 
                                        head_text="PREPCA"
                                        // project_img={Prepca}
                                        project_img={`${process.env.NEXT_PUBLIC_CDN_URL}PREP.webp`}
                                        desc_text={
                                            <>
                                                Prepca assists Chartered Accountancy students with their exam preparation. The platform offers a comprehensive mock test series for CA Foundation, Intermediate, 
                                                and Final levels. The tests are designed to simulate real exam conditions, with expert evaluations, personalized feedback, and reports to help students improve 
                                                their performance.
                                                 {/* The software ensured a seamless user experience, offering both online and center-based test-taking options. The platform is tailored to meet 
                                                the specific needs of CA aspirants. */}
                                            </>
                                        }
                                    />
                                </SwiperSlide>

                                <SwiperSlide>
                                    <ProjectCard 
                                        head_text="XLEY"
                                        // project_img={XLEY}
                                        project_img={`${process.env.NEXT_PUBLIC_CDN_URL}XLEY.webp`}
                                        desc_text={
                                            <>
                                                Xley is a revolutionary platform that is reshaping the creator ecosystem. We engineered Xley.ai to bridge the gap between brands and creators, leveraging cutting-edge 
                                                AI and machine learning. The platform's intuitive dashboard simplifies campaign management from planning to execution and monitoring. We prioritized user experience, 
                                                ensuring a smooth and efficient workflow for both brands and creators.
                                            </>
                                        }
                                    />
                                </SwiperSlide>

                                <SwiperSlide>
                                    <ProjectCard 
                                        head_text="UDYAMI"
                                        // project_img={Udyami}
                                        project_img={`${process.env.NEXT_PUBLIC_CDN_URL}udyami.webp`}
                                        desc_text={
                                            <>
                                                Udyami serves as the official portal for the Department of Industries, Government of Bihar, focusing on promoting entrepreneurship and supporting small-scale 
                                                industries within the state. 
                                                The scheme aims to encourage entrepreneurship among the youth by providing financial assistance and support for establishing various businesses. 
                                                {/* The Udyami portal provides comprehensive information about these schemes, including eligibility criteria, application procedures, project lists, and contact 
                                                details for district industry centers. It also offers resources such as project cost lists and success stories to guide and motivate prospective entrepreneurs. <br /><br />
                                                Codebucket Solutions played a key role in developing the Udyami Bihar website, which serves as a platform for empowering entrepreneurs in Bihar. The team 
                                                focused on creating an intuitive, user-friendly interface to streamline access to government schemes for small businesses. By incorporating advanced web technologies, 
                                                they ensured the site was responsive and scalable, making it accessible across all devices. Codebucket’s expertise in integrating backend functionalities 
                                                enabled smooth application processes, ensuring seamless data flow for users applying to various business development schemes. The website also emphasizes security, 
                                                ensuring the safe submission of sensitive information. */}
                                            </>
                                        }
                                    />
                                </SwiperSlide>

                                <SwiperSlide>
                                    <ProjectCard 
                                        head_text="QM Cloud"
                                        // project_img={QM}
                                        project_img={`${process.env.NEXT_PUBLIC_CDN_URL}QM.webp`}
                                        desc_text={
                                            <>
                                                QMCLOUD is a no-code platform that simplifies and accelerates cloud infrastructure deployment across multiple cloud providers, such as AWS and Azure. It offers an 
                                                intuitive interface featuring drag-and-drop functionality, AI assistance, and smart wizards, enabling users with minimal programming experience to compose, edit, 
                                                and deploy cloud infrastructure efficiently.
                                                 {/* The platform also supports integration with CI/CD tools, including GitHub Actions, and provides reusable templates that adhere 
                                                to security best practices, aiming to reduce deployment risks and costs. */}
                                            </>
                                        }
                                    />
                                </SwiperSlide>

                                <SwiperSlide>
                                    <ProjectCard 
                                        head_text="MAD INFLUENCE "
                                        project_img={`${process.env.NEXT_PUBLIC_CDN_URL}Mad.webp`}
                                        desc_text={
                                            <>
                                                MadInfluence is a dynamic platform designed to bridge the gap between brands and influential social media personalities. Our team embarked on this project with a 
                                                clear vision to create a seamless, intuitive, and visually compelling website that reflects MadInfluence's innovative approach to influencer marketing.
                                                {/* <br /> <br /> 
                                                The design philosophy centered on a clean, user-friendly interface that enables effortless navigation and information retrieval. We incorporated vibrant visuals 
                                                and interactive elements to capture the energy and dynamism of the influencer marketing landscape. */}
                                            </>
                                        }
                                    />
                                </SwiperSlide>

                                <SwiperSlide>
                                    <ProjectCard 
                                        head_text="SINGLE WINDOW CLEARANCE SYSTEM"
                                        project_img={`${process.env.NEXT_PUBLIC_CDN_URL}Single.webp`}
                                        desc_text={
                                            <>
                                                The single-window system is a digital platform that allows businesses and individuals to submit information and documents to multiple government agencies through a 
                                                single entry point. It streamlines administrative processes, reducing redundancy, delays, and compliance burdens.
                                                 {/* <br /> <br />
                                                SWCS, developed for the Government of Bihar, is a robust digital platform that streamlines the investment process by providing businesses with a one-stop solution for 
                                                approvals and complaint resolution. With a focus on efficiency, security, and user-friendliness, Codebucket Solutions engineered the SWCS portal to ensure seamless 
                                                navigation, quick application processing, and real-time tracking of requests. The platform integrates various government departments, making it easier for investors 
                                                to set up and expand businesses in Bihar. */}
                                            </>
                                        }
                                    />
                                </SwiperSlide>

                                <SwiperSlide>
                                    <ProjectCard 
                                        head_text="BIPARD"
                                        project_img={`${process.env.NEXT_PUBLIC_CDN_URL}Bparad.webp`}
                                        desc_text={
                                            <>
                                                THE BIPARD ERP platform integrates various institutional processes, facilitating better organizational management. BIPARD's ERP is a cornerstone in modernizing 
                                                its administrative and operational framework. By embracing this integrated approach, the institute enhances its capacity to deliver effective training programs, 
                                                manage resources efficiently, and uphold its commitment to public service excellence.
                                                 {/* <br /> <br />
                                                This project showcases our expertise in creating dynamic, user-friendly, and highly functional websites tailored to government and institutional needs. 
                                                The BIPARD website is designed to be a seamless digital platform for training programs and workshops. Our team focused on a clean, intuitive interface, 
                                                ensuring easy navigation for government officials, researchers, and the general public. We implemented a responsive design, making the platform accessible across 
                                                all devices while maintaining high performance and security standards. Security was a top priority, and we implemented state-of-the-art encryption and authentication 
                                                mechanisms to protect sensitive government data. */}
                                            </>
                                        }
                                    />
                                </SwiperSlide>
                                
                                <SwiperSlide>
                                    <ProjectCard 
                                        head_text="ABHIYAN BASERA "
                                        project_img={`${process.env.NEXT_PUBLIC_CDN_URL}abhiyanBasera.webp`}
                                        desc_text={
                                            <>
                                                Abhiyan Basera is a government initiative in Bihar aimed at providing residential land to landless families, particularly those from marginalized communities such as 
                                                Mahadalits, Scheduled Castes, Scheduled Tribes, Backward Classes, and Extremely Backward Classes. The program seeks to ensure these families have access to housing land, 
                                                thereby improving their living conditions and promoting social equity.
                                                 {/* These programs collectively aim to empower underprivileged communities by providing essential 
                                                resources for stable livelihoods. */}
                                            </>
                                    }
                                    />
                                </SwiperSlide>

                            </Swiper>
                        </div>
                    </div>
                    
                </div>

                <div className={classes.global_presence}>
                    <div className={classes.left_cities_list}>
                        <p className={classes.global_heading}>Global Presence</p>
                        <p className={classes.sub_head}>Checkout the global presence of Codebucket</p>
                        <p className={classes.city_head}>Cities</p>

                        

                        {activeDiv === 'div1' &&
                            <div className={classes.location_list}>
                                <ul>
                                    <li>Birmingham</li>
                                    <li>London</li>
                                    <li>Bristol</li>
                                </ul>
                            </div>
                        }

                        {activeDiv === 'div2' &&
                            <div className={classes.location_list}>
                                <ul>
                                    <li>New Delhi</li>
                                    <li>Gurugram</li>
                                    <li>Noida</li>
                                    <li>Pune</li>
                                    <li>Mumbai</li>
                                    <li>Kochi</li>
                                    <li>Patna</li>
                                </ul>
                                <ul>
                                    <li>Kolkata</li>
                                    <li>Maharashtra</li>
                                    <li>Rajasthan</li>
                                    <li>Bangalore</li>
                                </ul>
                            </div>
                        }

                        {activeDiv === 'div3' &&
                            <div className={classes.location_list}>
                                <ul>
                                    <li>New York</li>
                                    <li>Los Angeles</li>
                                </ul>
                            </div>
                        }
                    </div>

                    <div className={classes.right_map}>
                        <div className={classes.map_image}>
                            <div className={`${classes.img_wrapper} ${activeDiv === 'div1' ? classes.active : ''}`}>
                                <Image
                                src={`${process.env.NEXT_PUBLIC_CDN_URL}ukMap.webp`}
                                width={1253}
                                height={584}
                                alt="UK Map"
                                />
                            </div>
                            <div className={`${classes.img_wrapper} ${activeDiv === 'div2' ? classes.active : ''}`}>
                                <Image
                                src={`${process.env.NEXT_PUBLIC_CDN_URL}indiaMap.webp`}
                                width={1253}
                                height={584}
                                alt="India Map"
                                />
                            </div>
                            <div className={`${classes.img_wrapper} ${activeDiv === 'div3' ? classes.active : ''}`}>
                                <Image
                                src={`${process.env.NEXT_PUBLIC_CDN_URL}usaMap.webp`}
                                width={1253}
                                height={584}
                                alt="USA Map"
                                />
                            </div>
                        </div>

                        <div className={classes.map_buttons}>

                            <Button 
                                button_text="UK"
                                second_class={`${classes.map_button} ${activeDiv === 'div1' ? classes.active : ''}`} 
                                onClick={() => handleButtonClickTwo('div1')}
                                icon_two={<Image src={`${process.env.NEXT_PUBLIC_CDN_URL}UK.webp`} width={33} height={33} alt="" />}
                            />

                            <Button 
                                button_text="India"
                                second_class={`${classes.map_button} ${activeDiv === 'div2' ? classes.active : ''}`}
                                onClick={() => handleButtonClickTwo('div2')}
                                icon_two={<Image src={`${process.env.NEXT_PUBLIC_CDN_URL}IN.webp`} width={33} height={33} alt="" />}
                            />

                            <Button 
                                button_text="USA"
                                second_class={`${classes.map_button} ${activeDiv === 'div3' ? classes.active : ''}`}
                                onClick={() => handleButtonClickTwo('div3')}
                                icon_two={<Image src={`${process.env.NEXT_PUBLIC_CDN_URL}US.webp`} width={33} height={33} alt="" />}
                            />
                        </div>
                    </div>
                </div>
            </div>
    )
}

export default OurProjects;