import Design from "../../../assets/images/design.webp";
import Image from "next/image";

export const projects = [
  {
      head_title: "Development",
      description: "Building digital experiences that inspire innovation. We make scalable, secure, and high-performance applications using the latest technologies. We are passionate about solving real-world problems through modern digital solutions. We empower businesses, from startups to enterprises, with applications that drive growth and efficiency. ",
      color: "#fff",
      right_image: <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}development.webp`} alt="project image" width={856} height={918} />,
      tech_cards: [
          { tech_brand: "Kotlin", image: <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}kotlin.webp`} alt="project image" width={58} height={58} /> },
          { tech_brand: "ReactJs", image: <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}reactjs.webp`} alt="project image" width={58} height={58} /> },
          { tech_brand: "Python", image: <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}python.webp`} alt="project image" width={58} height={58} /> },
          { tech_brand: "NodeJs", image: <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}nodejs.webp`} alt="project image" width={58} height={58} /> },
          { tech_brand: "Cloud", image: <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}cloud.webp`} alt="project image" width={58} height={58} /> },
          { tech_brand: "AWS", image: <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}aws.webp`} alt="project image" width={58} height={58} /> },
        //   { tech_brand: "View More", image: <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}more.webp`} alt="project image" width={58} height={58} /> }
      ]
  },
  {
      head_title: "Design",
      description: "Empower your creativity with us and transform ideas into art for a better experience and solid strategies. Our design philosophy blends innovation with strategy to ensure that every creative element serves a purpose, making your brand not only visually stunning but also meaningfully connected to your audience.",
      color: "#fff",
      // right_image: <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}design.webp`} alt="project image" width={856} height={918} />,
      right_image: <Image src={Design} alt="project image" />,
      tech_cards: [
          { tech_brand: "Figma", image: <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}figma.webp`} alt="project image" width={58} height={58} /> },
          { tech_brand: "Sketch", image: <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}sketch.webp`} alt="project image" width={58} height={58} /> },
          { tech_brand: "Miro", image: <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}miro.webp`} alt="project image" width={58} height={58} /> },
          { tech_brand: "WebFlow", image: <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}webflow.webp`} alt="project image" width={58} height={58} /> },
          { tech_brand: "ChatGPT", image: <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}chatgpt.webp`} alt="project image" width={58} height={58} /> },
        //   { tech_brand: "View More", image: <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}more.webp`} alt="project image" width={58} height={58} /> }
      ]
  },
  {
      head_title: "Gen AI",
      description: "Empower your team with AI-powered tools that automate documentation, generate reports, and provide intelligent recommendations, helping you work smarter and achieve more in less time. Our Generative AI solutions are designed to seamlessly integrate into your existing workflows, reducing manual effort and boosting overall productivity.",
      color: "#fff",
      right_image: <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}analytics.webp`} alt="project image" width={856} height={918} />,
      tech_cards: [
          { tech_brand: "OpenAI GPT", image: <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}OpenAi.webp`} alt="project image" width={58} height={58} /> },
          { tech_brand: "Meta Llama", image: <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}Lamma.webp`} alt="project image" width={58} height={58} /> },
          { tech_brand: "Microsoft Copilot", image: <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}msCopilot.webp`} alt="project image" width={58} height={58} /> },
          { tech_brand: "Google Gemini", image: <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}gemini.webp`} alt="project image" width={58} height={58} /> },
          { tech_brand: "Github Copilot", image: <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}GithubCopilot.webp`} alt="project image" width={58} height={58} /> },
      ]
  },
];