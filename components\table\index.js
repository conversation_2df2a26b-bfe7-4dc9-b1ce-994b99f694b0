import React from "react";
import classes from "./styles.module.scss";
import PropTypes from 'prop-types';

const Table = ({ columns, rows, variant, ...props }) => {
    return(
        <table className={`${classes.custom_table} ${classes[variant]}`} {...props}>
            <thead>
                <tr>
                    {columns.map((column) => (
                        <th key={column.name}>{column.label}</th>
                    ))}
                </tr>
            </thead>
            <tbody>
                {rows.map((row, rowIndex) => (
                    <tr key={rowIndex}>
                        {columns.map((column) => (
                            <td key={column.name}>{row[column.name]}</td>
                        ))}
                    </tr>
                ))}
            </tbody>
        </table>
    )
}

Table.propTypes = {
    columns: PropTypes.arrayOf(
        PropTypes.shape({
            label: PropTypes.string.isRequired,
            name: PropTypes.string.isRequired,
        })
    ).isRequired,
    variant: PropTypes.string,
    rows: PropTypes.arrayOf(PropTypes.object).isRequired,
  };

export default Table;