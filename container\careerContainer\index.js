import { useRef, useEffect, useState } from "react";
import classes from "./styles.module.scss";
import JoinATeam from "./joinATeam";
import Department from "./department";
import LifeAtCB from "./lifeAtCB";

const CareerContainer = () => {

    const joinATeamRef = useRef(null);
    const departmentRef = useRef(null);
    const lifeAtCBRef = useRef(null);

    const [heightDifferences, setHeightDifferences] = useState({
        joinATeam: 0,
        department: 0,
        lifeAtCB: 0,
    });

    useEffect(() => {
        const handleResize = () => {
            const viewportHeight = window.innerHeight;
            setHeightDifferences({
                joinATeam: joinATeamRef.current ? joinATeamRef.current.clientHeight - viewportHeight : 0,
                department: departmentRef.current ? departmentRef.current.clientHeight - viewportHeight : 0,
                lifeAtCB: lifeAtCBRef.current ? lifeAtCBRef.current.clientHeight - viewportHeight : 0,
            });
        };

        handleResize();
        window.addEventListener('resize', handleResize);

        return () => {
            window.removeEventListener('resize', handleResize);
        };
    }, []);

    return(
        <div className={classes.career_container}>
            <div className={classes.career_content}>
                <div 
                    className={classes.view_content}
                    ref={joinATeamRef}
                    style={{
                        top: `-${heightDifferences.joinATeam}px`,
                    }}
                >
                    <JoinATeam />
                </div>
                <div 
                    className={classes.view_content}
                    ref={departmentRef}
                    style={{
                        top: `-${heightDifferences.department}px`,
                    }}
                >
                    <Department />
                </div>
                <div 
                    className={classes.view_content}
                    ref={lifeAtCBRef}
                    style={{
                        top: `-${heightDifferences.lifeAtCB}px`,
                    }}  
                >
                    <LifeAtCB />
                </div>
            </div>
        </div>
    )
}

export default CareerContainer;