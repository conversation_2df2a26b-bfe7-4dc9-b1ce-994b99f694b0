import { useEffect, useRef } from "react";
import PropTypes from "prop-types";
import classes from "./styles.module.scss";
import Button from "../button";

const CareerCard = ({ id, setActiveCard, activeCard, scrollDirection, title, description, image, buttonText }) => {
    const cardRef = useRef(null);

    useEffect(() => {
        const handleScroll = () => {
            if (cardRef.current) {
                const rect = cardRef.current.getBoundingClientRect();
                const windowHeight = window.innerHeight;

                const shouldActivateCard = 
                    (scrollDirection === "up" && rect.top <= windowHeight * 0.40 && rect.bottom >= windowHeight * 0.40) ||
                    (scrollDirection === "down" && rect.top <= windowHeight * 0.60 && rect.bottom >= windowHeight * 0.60);

                if (shouldActivateCard) {
                    setActiveCard(id);
                }
            }
        };

        window.addEventListener("scroll", handleScroll);

        return () => {
            window.removeEventListener("scroll", handleScroll);
        };
    }, [id, setActiveCard, scrollDirection]);

    return (
        <div
            ref={cardRef}
            className={`${classes.career_card_container} ${activeCard === id ? classes.active : ""}`}
        >
            <div className={classes.career_card_content}>
                <div className={classes.left_section}>
                    <p className={classes.main_header}>{title}</p>
                    <p className={classes.description}>{description}</p>
                    {/* <Button button_text={buttonText} variant="button_orange" second_class={classes.know_more_button} /> */}
                </div>
                <div className={classes.right_section}>
                    {image}
                </div>
            </div>
        </div>
    );
};

CareerCard.propTypes = {
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    setActiveCard: PropTypes.func.isRequired,
    activeCard: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    scrollDirection: PropTypes.string.isRequired,
    title: PropTypes.string.isRequired,
    description: PropTypes.string.isRequired,
    image: PropTypes.string.isRequired,
    buttonText: PropTypes.string.isRequired
};

export default CareerCard;