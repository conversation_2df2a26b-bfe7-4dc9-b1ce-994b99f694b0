import React, { useEffect, useState } from 'react';
import classes from "./styles.module.scss";
import GalaryPhoto from "@/components/galaryPhoto";
import XleyUI from "../../../assets/images/xleyUI.webp";

const FragmentedVision = () => {
    const [isScrolled, setIsScrolled] = useState(false);

    useEffect(() => {
        const handleScroll = () => {
            console.log("Scroll event detected", window.scrollY);
            if (window.scrollY > 0) {
                setIsScrolled(true);
            } else {
                setIsScrolled(false);
            }
        };

        window.addEventListener('scroll', handleScroll);

        return () => {
            window.removeEventListener('scroll', handleScroll);
        };
    }, []);

    useEffect(() => {
        console.log("isScrolled state:", isScrolled);
    }, [isScrolled]);

    return (
        <div className={classes.fragmented_vision_container}>
            <div className={classes.bg_image}></div>
            <div className={`${classes.half_bg_black} ${isScrolled ? classes.active : ''}`}></div>

            <div className={classes.fragmented_vision_content}>
                <div className={classes.fragment_vision_content_header}>
                    <div className={classes.left}>
                        <p className={classes.left_header}>From Fragmented Vision to Remarkable Reality</p>
                        <p className={classes.left_desc}>
                            Witness the power of innovative software development unfold. Explore our project gallery to discover how we transform ideas into impactful solutions for businesses like yours.
                        </p>
                    </div>

                    <div className={classes.right}>
                        <p className={classes.right_card}>AI/ML</p>
                        <p className={classes.right_card}>Blockchain</p>
                        <p className={classes.right_card}>E-Commerce</p>
                        <p className={classes.right_card}>Healthcare</p>
                        <p className={classes.right_card}>Mobile Innovation</p>
                        <p className={classes.right_card}>Custom Web Development</p>
                    </div>
                </div>

                <div className={`${classes.photo_galary_content} ${isScrolled ? classes.active : ''}`}>
                    <GalaryPhoto src={`${process.env.NEXT_PUBLIC_CDN_URL}galary1.webp`} second_class={`${classes.img_one} ${isScrolled ? classes.active : ''}`} />
                    <GalaryPhoto src={`${process.env.NEXT_PUBLIC_CDN_URL}galary3.webp`} second_class={`${classes.img_three} ${isScrolled ? classes.active : ''}`} />
                    <GalaryPhoto src={XleyUI} second_class={`${classes.img_two} ${isScrolled ? classes.active : ''}`} />
                </div>
            </div>
        </div>
    );
};

export default FragmentedVision;