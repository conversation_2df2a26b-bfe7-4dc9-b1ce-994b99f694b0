@import "../../assets/css/global.scss";

.header_container{
    width: 100%;
    height: auto;
    padding: 8px 40px;
    background-color: white;

    &.careers_bg {
        background-color: black;
    }

    .header_content{
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .left_section{
            display: flex;
            align-items: center;

            img{
                width: 52px;
                height: auto;
            }
        }

        .right_content{
            display: flex;
            align-items: center;
            overflow: hidden;

            .nav_menu{
                padding: 0px 28px;
                margin: 0;
                height: auto;
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: 20px;
                list-style-type: none;
                border-radius: 50px;
                background-color: white;
                
                &.grey_bg{
                    background-color: #242424;
                    margin-right: 20px;

                    .nam_items{
                        font-family: $lexLight;
                    }
                }

                .nav_link{
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 12px 10px;
                    position: relative;
                }

                .nav_link::after{
                    content: '';
                    position: absolute;
                    left: 50%;
                    transform: translate(-50%);
                    bottom: 0;
                    width: 0%;
                    height: 2px;
                    background-color: #E97800;
                    transition: 300ms;
                }

                .nav_link:hover::after{
                    width: 100%;
                }

                .nav_items{
                    font-family: $lexRegular;
                    cursor: pointer;
                    color: black;
                    font-size: 16px;

                    &.nav_items_white{
                        color: white;
                    }

                    &:hover{
                        opacity: 0.9;
                    }
                }
            }

            .button_section{
                display: flex;
                align-items: center;
                justify-content: flex-start;
                gap: 5px;

                .orange{
                    background: linear-gradient(34deg, #E97800, #D25500);
                }

                .black{
                    background: unset;
                    background-color: #242424;
                }
            }
        }

        .mobile_hamburger{
            display: none;

            img{
                width: 40px;
                height: auto;
            }
        }
    }

    .hamburger{
        position: fixed;
        width: 100%;
        top: 0;
        left: 0;
        padding: 20px 50px;
        z-index: 9;
        display: flex;
        justify-content: flex-end;
        align-items: center;

        .ham_button{
            all: unset;
            position: relative;
        }
    }
}

@media only screen and (max-width: 1366px){
    .header_container{
        padding: 7px 36px;
    
        .header_content{
            .left_section{
                img{
                    width: 48px;
                }
            }
    
            .right_content{
                .nav_menu{
                    padding: 0px 25px;
                    gap: 18px;
                    border-radius: 50px;
                    
                    .nav_link{
                        padding: 11px 9px;
                    }
    
                    .nav_link::after{
                        height: 2px;
                    }
    
                    .nav_items{
                        font-size: 15px;
                    }
                }
            }
        }
    
        .hamburger{
            padding: 15px 36px;
        }
    }
}

@media only screen and (max-width: 1180px){
    .header_container{
        padding: 6px 24px;

        .header_content{
            .left_section{
                img{
                    width: 44px;
                }
            }
    
            .right_content{
                .nav_menu{
                    padding: 0px 22px;
                    gap: 15px;
                    
                    .nav_link{
                        padding: 10px 8px;
                    }
    
                    .nav_items{
                        font-size: 14px;
                    }
                }
            }
        }
    
        .hamburger{
            padding: 15px 24px;
        }
    }
}

@media only screen and (max-width: 1024px){
    .header_container{
        padding: 20px 40px;   

        .header_content{
            .left_section{
                img{
                    width: 7vw;
                }
            }
    
            .right_content{
                gap: 3vw;

                .nav_menu{
                    padding: 0px 18px;
                    gap: 4vw;
                    
                    .nav_link{
                        padding: 8px 6px;
                    }
    
                    .nav_items{
                        font-size: 2.4vw;
                    }
                }

                .ll{
                    font-size: 2.4vw;
                    padding: 1.5vw 3vw;
                }
            }
        }
    
        .hamburger{
            padding: 30px 40px;
        }
    }
}

@media only screen and (max-width: 667px){
    .header_container{
        padding: 10px 15px;

        .header_content{

            .left_section{
                a{
                    img{
                        width: 12vw;
                    }
                }
            }
            .right_content{
                display: none;
            }

            .mobile_hamburger{
                display: block;

                button{
                    padding: 0;
                    display: flex;
                    align-items: center;

                    svg{
                        color: black;
                        font-size: 10vw;
                    }
                }
            }
        }

        .hamburger{
            padding: 18px 20px;
        }
    }
}