@import "../../assets/css/global.scss";

.career_card_container {
    width: 100%;
    height: auto;
    background-color: white;
    padding: 40px;
    border-radius: 20px;
    transition: 300ms ease-in-out;
    margin: 100px 0px;

    .career_card_content{
        display: flex;
        justify-content: space-between;

        .left_section{
            width: calc(100% - 140px);
            position: relative;
            background-color: white;

            .main_header{
                font-size: 70px;
                font-family: $ubuntuLight;
                margin-bottom: 0px;
                transition: 300ms;
            }
    
            .description{
                font-size: 0px;
                line-height: 0px;
                font-family: $lexLight;
                margin-bottom: 0px;
                transition: 300ms;
            }

            .know_more_button{
                display: none;
                position: absolute;
                bottom: 30px;
            }
        }

        .right_section{
            min-width: 121px;
            max-width: 121px;
            height: auto;
            aspect-ratio: 121/121;
            border-radius: 0px 48px 0px 0px;
            overflow: hidden;
            opacity: 0.8;
            transition: 300ms;
            background-color: white;

            img{
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }
    }
    

    &.active {
        padding: 40px;

        .career_card_content{
            width: 100%;
            height: auto;
            display: flex;
            justify-content: space-between;
            gap: 150px;
    
            .left_section{
                width: calc(100% - 450px);

                .main_header{
                    font-size: 110px;
                    margin-bottom: 10px;
                }
                .description{
                    font-size: 20px;
                    line-height: 30px;
                    margin-bottom: 40px;
                }
                .know_more_button{
                    display: block;
                }
            }
    
            .right_section{
                min-width: 428px;
                max-width: 428px;
                aspect-ratio: 428/459;
                opacity: 1;
            }
        }
    }
}

@media only screen and (max-width: 1366px){
    .career_card_container {
        padding: 35px;
    
        .career_card_content{
            .left_section{
                .main_header{
                    font-size: 60px;
                }
            }
    
            .right_section{
                aspect-ratio: 121/121;
            }
        }
        
    
        &.active {
            padding: 35px;
    
            .career_card_content{
                gap: 100px;
        
                .left_section{
                    .main_header{
                        font-size: 90px;
                    }
                    .description{
                        font-size: 18px;
                        line-height: 26px;
                        margin-bottom: 35px;
                    }
                }
        
                .right_section{
                    min-width: 400px;
                    max-width: 400px;
                    aspect-ratio: 428/459;
                    opacity: 1;
                }
            }
        }
    }
}

@media only screen and (max-width: 1280px){
    .career_card_container {
        &.active {
            .career_card_content{
                .right_section{
                    min-width: 370px;
                    max-width: 370px;
                }
            }
        }
    }
}

@media only screen and (max-width: 1180px){
    .career_card_container {
        padding: 30px;
        border-radius: 16px;
    
        .career_card_content{
            .left_section{
                .main_header{
                    font-size: 50px;
                }
            }
    
            .right_section{
                min-width: 101px;
                max-width: 101px;
                aspect-ratio: 121/121;
            }
        }
        
    
        &.active {
            padding: 30px;
    
            .career_card_content{
                gap: 80px;
        
                .left_section{
                    .main_header{
                        font-size: 75px;
                        margin-bottom: 6px;
                    }
                    .description{
                        font-size: 17px;
                        line-height: 25px;
                        margin-bottom: 30px;
                    }
                }
        
                .right_section{
                    min-width: 350px;
                    max-width: 350px;
                }
            }
        }
    }
}

@media only screen and (max-width: 1080px){
    .career_card_container {
        &.active {
            .career_card_content{
                .right_section{
                    min-width: 330px;
                    max-width: 330px;
                }
            }
        }
    }
}

@media only screen and (max-width: 980px){
    .career_card_container {
        padding: 25px;
        border-radius: 12px;
    
        .career_card_content{
            .left_section{
                .main_header{
                    font-size: 40px;
                }
    
            }
    
            .right_section{
                min-width: 90px;
                max-width: 90px;
            }
        }
        
    
        &.active {
            padding: 25px;
    
            .career_card_content{
                gap: 70px;
        
                .left_section{
                    .main_header{
                        font-size: 58px;
                    }
                    .description{
                        font-size: 16px;
                        line-height: 24px;
                        margin-bottom: 25px;
                    }
                }
        
                .right_section{
                    min-width: 300px;
                    max-width: 300px;
                }
            }
        }
    }
}

@media only screen and (max-width: 880px){
    .career_card_container {
        &.active {
            .career_card_content{
                .right_section{
                    min-width: 260px;
                    max-width: 260px;
                }
            }
        }
    }
}

@media only screen and (max-width: 780px){
    .career_card_container {
        padding: 20px;
        border-radius: 10px;
    
        .career_card_content{
            .left_section{
                .main_header{
                    font-size: 35px;
                }
            }
    
            .right_section{
                min-width: 80px;
                max-width: 80px;
            }
        }
        
    
        &.active {
            padding: 20px;
    
            .career_card_content{
                gap: 60px;
        
                .left_section{
                    .main_header{
                        font-size: 48px;
                    }
                    .description{
                        font-size: 13px;
                        line-height: 23px;
                        margin-bottom: 20px;
                    }
                }
        
                .right_section{
                    min-width: 220px;
                    max-width: 220px;
                }
            }
        }
    }
}

@media only screen and (max-width: 667px){
    .career_card_container {
        padding: 15px;
        border-radius: 10px;
        margin: 10vw 0;
    
        .career_card_content{
            .left_section{
                .main_header{
                    font-size: 6vw;
                }
            }
    
            .right_section{
                min-width: 15vw;
                max-width: 15vw;
            }
        }
        
    
        &.active {
            padding: 20px;
    
            .career_card_content{
                gap: unset;
                flex-direction: column-reverse;
        
                .left_section{
                    margin-top: 6vw;
                    width: 100%;

                    .main_header{
                        font-size: 10vw;
                        margin-bottom: 0px;
                    }
                    .description{
                        font-size: 3.5vw;
                        line-height: unset;
                        margin-bottom: 20px;
                    }

                    .know_more_button{
                        position: unset;
                    }
                }
        
                .right_section{
                    min-width: 100%;
                    max-width: 100%;
                    height: 60vw;
                    aspect-ratio: unset;
                }
            }
        }
    }
}