@import "../../assets/css/global.scss";

.location_card{
    background-color: white;

    .location_icon{
        width: 58px;
        height: auto;
        margin-bottom: 15px;
    }

    .location_name{
        font-size: 18px;
        font-family: $lexRegular;
        margin-bottom: 20px;
    }

    .location_Address{
        font-size: 18px;
        font-family: $lexLight;
        margin-bottom: 15px;
    }

    
    .view_link{
        background-color: white;
        
        a{
            text-decoration: none;
            color: #E58900;
            font-size: 18px;
            font-family: $lexRegular;
        }
    }
}

@media only screen and (max-width: 1366px){
    .location_card{
        .location_icon{
            width: 54px;
        }

        .location_name{
            font-size: 16px;
            margin-bottom: 17px;
        }

        .location_Address{
            font-size: 16px;
        }

        .view_link{
            a{
                font-size: 16px;
            }
        }
    }
}

@media only screen and (max-width: 1180px){
    .location_card{
        .location_icon{
            width: 50px;
            margin-bottom: 13px;
        }

        .location_name{
            font-size: 14.5px;
            margin-bottom: 10px;
        }

        .location_Address{
            font-size: 14.5px;
            margin-bottom: 12px;
        }

        .view_link{
            a{
                font-size: 14.5px;
            }
        }
    }
}

@media only screen and (max-width: 980px){
    .location_card{
        .location_icon{
            width: 40px;
            margin-bottom: 10px;
        }

        .location_name{
            font-size: 13px;
            margin-bottom: 7px;
        }

        .location_Address{
            font-size: 13px;
            margin-bottom: 10px;
        }

        .view_link{
            a{
                font-size: 13px;
            }
        }
    }
}

@media only screen and (max-width: 780px){
    .location_card{
        .location_icon{
            width: 30px;
            margin-bottom: 7px;
        }

        .location_name{
            font-size: 11px;
            margin-bottom: 5px;
        }

        .location_Address{
            font-size: 11px;
            margin-bottom: 7px;
        }

        .view_link{
            a{
                font-size: 11px;
            }
        }
    }
}

@media only screen and (max-width: 667px){
    .location_card{
        .location_icon{
            width: 4vw;
            margin-bottom: 1.5vw;
        }

        .location_name{
            font-size: 1.8vw;
            margin-bottom: 1vw;
        }

        .location_Address{
            font-size: 1.8vw;
            margin-bottom: 1.6vw;
        }

        .view_link{
            a{
                font-size: 1.8vw;
            }
        }
    }
}