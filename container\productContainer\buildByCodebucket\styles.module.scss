.build_by_codebucket_container{
    width: 100%;
    height: 65vw;
    padding: 80px 40px;
    background-color: black;
    overflow: hidden;

    .build_by_codebucket_content{
        position: relative;

        .image_one{
            width: 25%;
            position: absolute;
            left: 7vw;
            top: 0;

            img{
                filter: blur(0.5px);
                opacity: 0.7;
            }
        }

        .image_two{
            position: absolute;
            right: 0;
            top: 3vw;
            width: 47%;

            img{
                filter: blur(0.5px);
                opacity: 0.7;
            }
        }

        .image_three{
            position: absolute;
            left: 0;
            top: 20vw;
            width: 46%;

            img{
                filter: blur(0.5px);
                opacity: 0.7;
            }
        }

        .image_four{
            position: absolute;
            top: 35vw;
            right: 10vw;
            width: 30%;

            img{
                filter: blur(0.5px);
                opacity: 0.7;
            }
        }

        .image_one, .image_two, .image_three, .image_four{
            img{
                transition: 200ms;
                
                &:hover{
                    filter: unset;
                    opacity: 1;
                }
            }
        }

        .cb_tag_wrapper{
            display: flex;
            justify-content: center;
        }

        .cb_tag{
            position: absolute;  
            width: auto;
            background-color: black;
            display: flex; 
            align-items: center;
            gap: 15px;
            border-radius: 4px;
            padding: 15px 50px;

            .tag_image{
                width: 3.5vw;
                height: auto;
            }

            .tag_text{
                font-size: 1.7vw;
                color: white;
                font-weight: 600;
                margin-top: -5px;
            }
        }
    }
}

@media only screen and (max-width: 667px){
    .build_by_codebucket_container{
        height: 80vw;
        padding: 40px 15px;
    
        .build_by_codebucket_content{
    
            .image_one{
                width: 40%;
                left: 0vw;
    
                img{
                    filter: unset;
                    opacity: 1;
                }
            }
    
            .image_two{
                top: 2vw;
    
                img{
                    filter: unset;
                    opacity: 1;
                }
            }
    
            .image_three{
                top: 27vw;
                width: 46%;

                img{
                    filter: unset;
                    opacity: 1;
                }
            }
    
            .image_four{
                top: 33vw;
                right: 0vw;
                width: 40%;
    
                img{
                    filter: unset;
                    opacity: 1;
                }
            }
    
            .cb_tag{
                gap: 2vw;
                padding: 10px 15px;
    
                .tag_image{
                    width: 10vw;
                    height: auto;
                }
    
                .tag_text{
                    font-size: 4.5vw;
                    margin-top: -1vw;
                }
            }
        }
    }
}