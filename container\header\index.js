import classes from "./styles.module.scss";
import PropTypes from 'prop-types';
import Image from "next/image";
import Button from "../../components/button";
import Link from "next/link";
import { useRouter } from "next/router";
import { useRef, useState, useEffect } from "react";
import LeftMenuContainer from "../leftMenuContainer";
import ColorHam from "./colorHam";
import ModalLayout from "@/components/modalLayout";
import MenuRoundedIcon from '@mui/icons-material/MenuRounded';

const Header = ({ showMenus, setShowMenus }) => {
    const router = useRouter();
    const isCurrentPage = router.pathname === "/career";

    const [isHamburgerVisible, setIsHamburgerVisible] = useState(false);
    const headerRef = useRef(null);

    useEffect(() => {
        const headerElement = headerRef.current;
        const observer = new IntersectionObserver(
            ([entry]) => {
                setIsHamburgerVisible(!entry.isIntersecting);
            },
            {
                root: null,
                threshold: 0,
            }
        );

        if (headerElement) {
            observer.observe(headerElement);
        }

        return () => {
            if (headerElement) {
                observer.unobserve(headerElement);
            }
        };
    }, []);

    const handleShowMenu = () => {
        setShowMenus(true); 
    };

    const handleHideMenu = () => {
        setShowMenus(false);
    };

    return (
        <div className={`${classes.header_container} ${isCurrentPage ? classes.careers_bg : ''}`}>
            <div ref={headerRef} className={classes.header_content}>
                <div className={classes.left_section}>
                    <Link href={"/"}>
                        {
                            isCurrentPage? 
                             <></>:
                            <Image  
                                src={`${process.env.NEXT_PUBLIC_CDN_URL}LogoWhite.webp`} 
                                className={classes.logo_white} 
                                alt="logo" 
                                width={105}
                                height={80}
                            />
                        }
                    </Link>
                </div>

                <div className={classes.right_content}>
                    <ul className={`${classes.nav_menu} ${isCurrentPage ? classes.grey_bg : ''}`}>
                        <Link href={"/product"} className={classes.nav_link}>
                            <li className={`${classes.nav_items} ${isCurrentPage ? classes.nav_items_white : ''}`}>Products</li>
                        </Link>
                        <Link href={"https://codebucketlab.com/"} target="_blank" className={classes.nav_link}>
                            <li className={`${classes.nav_items} ${isCurrentPage ? classes.nav_items_white : ''}`}>cB Lab</li>
                        </Link>
                        <Link href={"/career"} className={classes.nav_link}>
                            <li className={`${classes.nav_items} ${isCurrentPage ? classes.nav_items_white : ''}`}>Careers</li>
                        </Link>
                    </ul>
                    <div className={classes.button_section}>
                        <Link href={"/contact-us"}>
                            <Button 
                                button_text="Contact Us" 
                                variant="button_orange" 
                                second_class={`${classes.ll} ${isCurrentPage ? classes.black : ''}`}
                            />
                        </Link>
                    </div>
                </div>

                <div className={classes.mobile_hamburger}>
                    <Button icon={<MenuRoundedIcon />} onClick={handleShowMenu} />
                </div>
            </div>
            {
                isHamburgerVisible && 
                    <div className={classes.hamburger}>
                        <button className={classes.ham_button} onClick={handleShowMenu}>
                            <ColorHam />
                        </button>
                    </div>
            }
            <ModalLayout
                open={showMenus}
                close={handleHideMenu}
                hideHeader={true}
                slideDirection="right"
            >
                <div className={`${classes.left_menu_container} ${showMenus ? classes.show : ''}`}>
                    <LeftMenuContainer onClose={handleHideMenu} />
                </div>
            </ModalLayout>
        </div>
    );
};

Header.propTypes = {
    showMenus: PropTypes.bool.isRequired,
    setShowMenus: PropTypes.func.isRequired,
};

export default Header;