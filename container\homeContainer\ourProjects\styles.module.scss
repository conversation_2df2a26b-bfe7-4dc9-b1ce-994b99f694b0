@import "../../../assets/css/global.scss";

.our_projects{
    width: 100%;
    min-height: 100vh;
    padding: 80px 40px;
    background-color: black;
    border-radius: 30px 30px 0px 0px;

    .first_slider_content{
        width: 100%;
        height: auto;
        display: flex;
        justify-content: space-between;
        margin-bottom: 100px;

        .left_content{
            width: 30%;
            position: relative;

            .left_head{
                font-size: 36px;
                font-family: $ubuntuMedium;
                margin-bottom: 15px;
                color: white;
            }

            .sub_text{
                font-size: 18px;
                line-height: 28px;
                font-family: $lexLight;
                margin-bottom: 34px;
                color: white;
            }

            .right_swiper_navigation_button{
                position: absolute;
                bottom: 0;
                left: 0;
                display: flex;
                gap: 30px;

                button{
                    margin: 0;
                    padding: 0;

                    img{
                        width: 30px;
                        height: auto;
                    }
                }
            }
        }

        .right_slider_container{
            width: 65%;

            .right_slider_content{
                width: 100%;    
                height: auto;
                display: flex;
                gap: 40px;

                .custom_swiper {
                    padding-bottom: 40px;
                }

                .card_wrapper{
                    width: 50%;
                    height: auto;
                }
            }
        }
    }

    .global_presence{
        width: 100%;
        min-height: 710px;
        overflow: hidden;
        background-color: white;
        padding: 80px 0px;
        border-radius: 30px;
        display: flex;
        align-items: flex-start;
        justify-content: space-evenly;
        gap: unset;
        transition: 300ms ease-in-out;

        .left_cities_list{
            background-color: white;
            .global_heading{
                font-size: 62px;
                font-family: $ubuntuLight;
                margin-bottom: 15px;
            }

            .sub_head{
                font-size: 20px;
                font-family: $lexRegular;
                margin-bottom: 50px;
            }

            .city_head{
                font-size: 18px;
                font-family: $lexRegular;
                border-bottom: 1px solid black;
                width: 400px;
                padding-left: 15px;
                padding-bottom: 20px;
                margin-bottom: 30px;
            }

            .location_list{
                display: flex;
                gap: 100px;
                background-color: white;

                ul{
                    padding-left: 40px;

                    li{
                        margin-bottom: 15px;
                        font-size: 15px;
                        font-family: $lexRegular;
                    }
                }
            }
        }

        .right_map{
            width: 625px;
            height: auto;
            overflow: hidden;
            background-color: white;

            .map_image {
                width: 100%;
                height: 300px;
                position: relative;
                background-color: white;

                .img_wrapper {
                    position: absolute;
                    top: 0; left: 0;
                    width: 100%;
                    height: 100%;
                    visibility: hidden;
                    background-color: white;
                }

                .active {
                    visibility: visible;
                }

                img {
                    width: 100%;
                    height: auto;
                    object-fit: cover;
                }
            }

            .map_buttons{
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 20px;
                background-color: white;

                .map_button{
                    display: flex;
                    align-items: center;
                    transition: 200ms ease-in-out;
                    opacity: 0.6;
                    background-color: black;

                    img{
                        margin-left: 10px;
                        width: 25px;
                        height: 25px;
                    }

                    &:hover,
                    &.active{
                        filter: unset;
                        opacity: 1;
                    }
                }

                .active {
                    background-color: black;
                    color: white;
                }
            }
        }
    }
}

@media only screen and (max-width: 1366px){
    .our_projects{
        padding: 72px 36px;
    
        .first_slider_content{
            margin-bottom: 80px;
    
            .left_content{
                width: 30%;
    
                .left_head{
                    font-size: 30px;
                    margin-bottom: 10px;
                }
    
                .sub_text{
                    font-size: 16px;
                    line-height: 25px;
                    margin-bottom: 25px;
                }
    
                .right_swiper_navigation_button{
                    gap: 30px;
    
                    button{
                        img{
                            width: 25px;
                        }
                    }
                }
            }
    
            .right_slider_container{
                width: 65%;
    
                .right_slider_content{
                    width: 100%;    
                    gap: 20px;
                }
            }
        }
    
        .global_presence{
            padding: 70px 40px;
            border-radius: 30px;
            gap: 80px;
            min-height: 550px;
    
            .left_cities_list{
                .global_heading{
                    font-size: 50px;
                    margin-bottom: 15px;
                }
    
                .sub_head{
                    font-size: 17px;
                    margin-bottom: 40px;
                }
    
                .city_head{
                    font-size: 15px;
                    width: 350px;
                    padding-bottom: 10px;
                }
    
                .location_list{
                    gap: 70px;
    
                    ul{
                        li{
                            margin-bottom: 14px;
                            font-size: 12px;
                        }
                    }
                }
            }
    
            .right_map{
                width: 450px;
    
                .map_image{
                    margin-bottom: 0px;
                    height: 250px;
                }
    
                .map_buttons{
                    gap: 15px;
    
                    .map_button{
                        font-size: 14px;

                        img{
                            margin-left: 10px;
                            width: 20px;
                            height: 20px;
                        }
                    }
                }
            }
        }
    }
}


@media only screen and (max-width: 1180px){
    .our_projects{
        padding: 48px 24px;
    
        .first_slider_content{
            margin-bottom: 60px;
    
            .left_content{
                width: 30%;
    
                .left_head{
                    font-size: 26px;
                    margin-bottom: 10px;
                }
    
                .sub_text{
                    font-size: 14px;
                    line-height: 22px;
                    margin-bottom: 20px;
                }
    
                .right_swiper_navigation_button{
                    gap: 20px;
    
                    button{
                        img{
                            width: 22px;
                        }
                    }
                }
            }
        }
    
        .global_presence{
            padding: 50px 30px;
            border-radius: 20px;
            gap: 50px;
            min-height: 400px;
    
            .left_cities_list{
                .global_heading{
                    font-size: 40px;
                    margin-bottom: 10px;
                }
    
                .sub_head{
                    font-size: 14.5px;
                    margin-bottom: 30px;
                }
    
                .city_head{
                    font-size: 13px;
                    width: 350px;
                    padding-left: 10px;
                    padding-bottom: 10px;
                    margin-bottom: 15px;
                }
    
                .location_list{
                    gap: 50px;
    
                    ul{
                        li{
                            margin-bottom: 10px;
                            font-size: 11px;
                        }
                    }
                }
            }
    
            .right_map{
                width: 370px;
    
                .map_image{
                    margin-bottom: 0px;
                    height: 200px;
                }
    
                .map_buttons{
                    gap: 10px;
    
                    .map_button{
                        font-size: 12px;
                        padding: 10px 20px;
                        height: auto;

                        img{
                            margin-left: 6px;
                            width: 15px;
                            height: 15px;
                        }
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 1080px){
    .our_projects{
        min-height: auto;
    }
}

@media only screen and (max-width: 980px){
    .our_projects{
        padding: 40px 20px;

        .first_slider_content{
            margin-bottom: 50px;
    
            .left_content{
    
                .left_head{
                    font-size: 22px;
                    margin-left: -20px;
                }
    
                .sub_text{
                    font-size: 12px;
                    line-height: 16px;
                    margin-bottom: 15px;
                }
    
                .right_swiper_navigation_button{
                    gap: 15px;
    
                    button{
                        img{
                            width: 18px;
                        }
                    }
                }
            }
        }
    
        .global_presence{
            padding: 30px;
            border-radius: 16px;
            min-height: 280px;
    
            .left_cities_list{
                .global_heading{
                    font-size: 35px;
                }
    
                .sub_head{
                    font-size: 12px;
                    margin-bottom: 30px;
                }
    
                .city_head{
                    font-size: 11px;
                    width: 230px;
                    padding-left: 8px;
                    padding-bottom: 8px;
                    margin-bottom: 15px;
                }
    
                .location_list{
                    gap: 20px;
    
                    ul{
                        li{
                            margin-bottom: 7px;
                            font-size: 10px;
                        }
                    }
                }
            }
    
            .right_map{
                width: 320px;
    
                .map_image{
                    margin-bottom: 30px;
                }
    
                .map_buttons{
                    gap: 10px;
    
                    .map_button{
                        font-size: 10px;
                        padding: 7px 12px;
                        height: auto;

                        img{
                            margin-left: 5px;
                            width: 12px;
                            height: 12px;
                        }
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 780px){
    .our_projects{
        padding: 32px 16px;

        .first_slider_content{
            margin-bottom: 40px;
    
            .left_content{
                .left_head{
                    font-size: 18px;
                    margin-bottom: 5px;
                    margin-left: -20px;
                }
    
                .sub_text{
                    font-size: 11px;
                    line-height: 16px;
                    margin-bottom: 10px;
                }
    
                .right_swiper_navigation_button{
                    gap: 15px;
    
                    button{
                        img{
                            width: 18px;
                        }
                    }
                }
            }
        }
    
        .global_presence{
            padding: 20px;
            border-radius: 16px;
            gap: 30px;
            min-height: 350px;
    
            .left_cities_list{
                .global_heading{
                    font-size: 34px;
                    margin-bottom: 6px;
                }
    
                .sub_head{
                    font-size: 13px;
                    margin-bottom: 25px;
                }
    
                .city_head{
                    font-size: 10px;
                    width: 200px;
                    padding-left: 6px;
                    padding-bottom: 6px;
                    margin-bottom: 10px;
                }
    
                .location_list{
                    gap: 20px;
    
                    ul{
                        margin-left: -10px;
                        li{
                            margin-bottom: 7px;
                            font-size: 12px;
                        }
                    }
                }
            }
    
            .right_map{
                width: 260px;
    
                .map_image{
                    margin-bottom: 20px;
                }
    
                .map_buttons{
                    gap: 10px;
    
                    .map_button{
                        font-size: 8px;
                        padding: 5px 10px;
                        height: auto;

                        img{
                            width: 10px;
                            height: 10px;
                        }
                    }
                }
            }
        }
    }
}


@media only screen and (max-width: 667px){
    .our_projects{
        padding: 25px 15px;

        .first_slider_content{
            display: block;
            margin-bottom: 50px;

            .left_content{
                width: 100%;
                margin-bottom: 40px;

                .left_head{
                    font-size: 25px;
                    margin-bottom: 15px;
                    margin-left: unset;
                }

                .sub_text{
                    font-size: 14px;
                    line-height: unset;
                    margin-bottom: 20px;
                }

                .right_swiper_navigation_button{
                    display: none;
                }
            }

            .right_slider_container{
                width: 100%;
            }
        }

        .global_presence{
            min-height: unset;
            height: auto;
            overflow: auto;
            background-color: white;
            padding: 20px;
            border-radius: 20px;
            display: flex;
            align-items: flex-start;
            justify-content: flex-start;
            gap: unset;
            flex-direction: column-reverse;
    
            .left_cities_list{
                width: 100%;
            background-color: white;


                .global_heading{
                    font-size: 10vw;
                    margin-bottom: 3vw;
            background-color: white;

                }
    
                .sub_head{
                    font-size: 4vw;
                    margin-bottom: 6vw;
                    font-family: $lexLight;
            background-color: white;

                }
    
                .city_head{
                    font-size: 4vw;
                    width: 100%;
                    padding-left: 2vw;
                    padding-bottom: 3vw;
                    margin-bottom: 6vw;
            background-color: white;

                }
    
                .location_list{
                    gap: 10vw;
            background-color: white;

    
                    ul{
                        padding-left: 40px;
    
                        li{
                            margin-bottom: 3vw;
                            font-size: 3.3vw;
                        }
                    }
                }
            }
    
            .right_map{
                width: 100%;
                margin-bottom: 10vw;
    
                .map_image{
                    width: 100%;
                    height: 45vw;
                    margin-bottom: 0vw;
                    background-color: white;
                }
    
                .map_buttons{
                    gap: 6vw;
                    background-color: white;
    
                    .map_button{
                        display: flex;
                        align-items: center;
                        transition: 200ms ease-in-out;
                        opacity: 0.6;
                        background-color: black;
                        font-size: 3vw;
    
                        img{
                            margin-left: 10px;
                            width: 5vw;
                            height: 5vw;
                        }
    
                        &:hover,
                        &.active{
                            filter: unset;
                            opacity: 1;
                        }
                    }
    
                    .active {
                        background-color: black;
                        color: white;
                    }
                }
            }
        }
    }
}