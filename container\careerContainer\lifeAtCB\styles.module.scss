@import "../../../assets/css/global.scss";

.life_at_cb{
    width: 100%;
    min-height: 100vh;
    background-color: white;
    padding: 80px 40px;
    background-image: url('https://prod-1.static.codebuckets.in/file/codebucket-production-public/codebucket-official-website/gridOne.webp'),
                    url('https://prod-1.static.codebuckets.in/file/codebucket-production-public/codebucket-official-website/gridTwo.webp');
    background-repeat: no-repeat;
    background-position: left top, top right;
    background-size: 50% auto;
    border-radius: 30px;

    .life_at_cb_header{
        text-align: center;
        font-size: 128px;
        font-family: $ubuntuLight;
    }

    .life_at_cb_header_desc{
        text-align: center;
        font-size: 20px;
        font-family: $lexLight;
        margin-bottom: 20px;
    }

    .hr_line{
        background-color: #E58900;
        height: 2px;
        width: 100%;
    }

    .our_company_location{
        width: 100%;
        height: auto;
        display: flex;
        justify-content: space-around;
        align-items: flex-start;
        margin-top: 70px;
    }

    .our_photo_gallery{
        width: 100%;
        height: auto;
        margin-top: 150px;

        .image_holder{
            width: 100%;
            height: auto;
            aspect-ratio: 280/250;
            border-radius: 28px;
            overflow: hidden;

            .gallery_photo{
                width: 100%;
                height: 100%;
                object-fit: cover;
                transition: 300ms ease-in-out;
    
                &:hover{
                    transform: scale(1.1);
                }
            }   
        }
    }
}

@media only screen and (max-width: 1366px){
    .life_at_cb{
        padding: 72px 36px;
    
        .life_at_cb_header{
            font-size: 110px;
        }
    
        .life_at_cb_header_desc{
            font-size: 16.5px;
        }
    
        .our_company_location{
            margin-top: 60px;
        }
    
        .our_photo_gallery{
            margin-top: 130px;
    
            .image_holder{
                border-radius: 24px;
            }
        }
    }
}

@media only screen and (max-width: 1180px){
    .life_at_cb{
        padding: 48px 24px;
    
        .life_at_cb_header{
            font-size: 95px;
        }
    
        .life_at_cb_header_desc{
            font-size: 14.5px;
        }
    
        .our_company_location{
            margin-top: 50px;
        }
    
        .our_photo_gallery{
            margin-top: 100px;
    
            .image_holder{
                border-radius: 24px;
            }
        }
    }
}

@media only screen and (max-width: 1080px){
    .life_at_cb{
        min-height: auto;
    }
}

@media only screen and (max-width: 980px){
    .life_at_cb{
        padding: 40px 20px;
    
        .life_at_cb_header{
            font-size: 85px;
        }
    
        .life_at_cb_header_desc{
            font-size: 13px;
            margin-bottom: 15px;
        }
    
        .hr_line{
            height: 1px;
        }
    
        .our_company_location{
            margin-top: 40px;
        }
    
        .our_photo_gallery{
            margin-top: 80px;
    
            .image_holder{
                border-radius: 20px;
            }
        }
    }
}

@media only screen and (max-width: 780px){
    .life_at_cb{
        padding: 32px 16px;
    
        .life_at_cb_header{
            font-size: 65px;
        }
    
        .life_at_cb_header_desc{
            font-size: 11px;
            margin-bottom: 10px;
        }
    
        .our_company_location{
            margin-top: 30px;
        }
    
        .our_photo_gallery{
            margin-top: 50px;
    
            .image_holder{
                border-radius: 16px;
            }
        }
    }
}

@media only screen and (max-width: 667px){
    .life_at_cb{
        padding: 32px 15px;
    
        .life_at_cb_header{
            font-size: 9vw;
        }
    
        .life_at_cb_header_desc{
            font-size: 2.4vw;
            margin-bottom: 1.6vw;
        }
    
        .our_company_location{
            margin-top: 3vw;
        }
    
        .our_photo_gallery{
            margin-top: 6vw;
    
            .image_holder{
                border-radius: 2vw;
            }
        }
    }
}