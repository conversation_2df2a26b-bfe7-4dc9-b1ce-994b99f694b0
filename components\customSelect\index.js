import classes from './styles.module.scss';
import PropTypes from 'prop-types';

const CustomSelect = ({ label, options, value, onChange, defaultOption }) => {
  return (
    <div className={classes.custom_select}>
        {label && <label className={classes.label}>{label}</label>}
        <select className={classes.select} value={value} onChange={onChange}>
            {defaultOption && <option value="">{defaultOption}</option>}
            {options.map((option, index) => (
            <option key={index} value={option.value}>
                {option.label}
            </option>
            ))}
        </select>
    </div>
  );
};

CustomSelect.propTypes = {
    label: PropTypes.string,
    options: PropTypes.arrayOf(
        PropTypes.shape({
        value: PropTypes.string.isRequired,
        label: PropTypes.string.isRequired,
        })
    ).isRequired,
    value: PropTypes.string.isRequired,
    onChange: PropTypes.func.isRequired,
    defaultOption: PropTypes.string,
};

CustomSelect.defaultProps = {
    label: '',
    defaultOption: 'Select',
};

export default CustomSelect;