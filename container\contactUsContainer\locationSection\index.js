import classes from "./styles.module.scss";
import LocationSlideCard from "@/components/locationSliderCard";
import { useState } from "react";

const LocationSection = () => {

  const [expandedCard, setExpandedCard] = useState(1); 
  const handleCardClick = (cardId) => {
      if (expandedCard !== cardId) {
          setExpandedCard(cardId);
      }
  };

    const cardData = [
        {
            id: 1,
            location_name: "Patna",
            map: <iframe 
                    title=""
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3597.809340286761!2d85.13453017606368!3d25.61125101480436!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x39ed59793e44eb07%3A0x742fce9944041b10!2sCodebucket%20Solutions%20Private%20Limited!5e0!3m2!1sen!2sin!4v1741042465872!5m2!1sen!2sin" 
                    allowFullscreen
                    loading="lazy" 
                    referrerPolicy="no-referrer-when-downgrade"
                    className={classes.embed_map_link}></iframe>,

            company_address: "Opposite Gate No 93, Digha Danapur Road, Patna - 800013",
            contact_num: "+91 99950 08671",
            contact_mail: "<EMAIL>",
        },
        {
            id: 2,
            location_name: "Kochi",
            map: <iframe 
                    title=""
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d7857.16771684508!2d76.3230823935791!3d10.051156700000012!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3b080d6cc1181729%3A0xcee781620049bb80!2sCodebucket%20Solutions%20Private%20Limited!5e0!3m2!1sen!2sin!4v1741042227696!5m2!1sen!2sin" 
                    allowFullscreen
                    loading="lazy" 
                    referrerPolicy="no-referrer-when-downgrade"
                    className={classes.embed_map_link}></iframe>,

            company_address: "HMT Colony,Kalamassery, Kochi, Kerala.",
            contact_num: "+91 99950 08671",
            contact_mail: "<EMAIL>",
        },
        {
            id: 3,
            location_name: "Bangalore",
            map: <iframe
                    title="" 
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3887.8572035403877!2d77.60078047507662!3d12.980983687335192!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3bae173ecba2d963%3A0xdb979f5f7974e8da!2sWeWork%20Prestige%20Central!5e0!3m2!1sen!2sin!4v1741041528313!5m2!1sen!2sin" 
                    allowFullscreen
                    loading="lazy" 
                    referrerPolicy="no-referrer-when-downgrade"
                    className={classes.embed_map_link}></iframe>,
            company_address: "WeWork Prestige Central, 36, Infantry Road, Bangalore, Karnantaka 560001",
            contact_num: "+91 99950 08671",
            contact_mail: "<EMAIL>",
        },
    ];

    return(
        <div className={classes.location_container}>
            <div className={classes.location_content}>
            {
                cardData.map(card => (
                    <LocationSlideCard
                        key={card.id}
                        location_name={card.location_name}
                        map={card.map}
                        company_address={card.company_address}
                        contact_num={card.contact_num}
                        contact_mail={card.contact_mail}
                        isExpanded={expandedCard === card.id}
                        onClick={() => handleCardClick(card.id)}
                    />
                ))
            }
            </div>
        </div>
    )
}

export default LocationSection;