'use client';

import { motion, useScroll, useTransform } from 'framer-motion';
import classes from "./styles.module.scss";
import GalaryPhoto from "@/components/galaryPhoto";
import Image from "next/image";
import YtChampions from "../../../assets/images/YtChampions.webp";
import Bhavani from "../../../assets/images/bhavani.webp";
import Biprad from "../../../assets/images/biprad.webp";
import Prepca from "../../../assets/images/prepca.webp";

const BuildByCodebucket = () => {
    const { scrollYProgress } = useScroll();

    // Define transformation for horizontal movement
    const moveLeft = useTransform(scrollYProgress, [0, 1], [0, -70]);
    const moveRight = useTransform(scrollYProgress, [0, 1], [0, 70]);
    const moveDown = useTransform(scrollYProgress, [0, 1], [0, 1800]);

    const transition = {
        duration: 0,
      }

    return (
        <div className={classes.build_by_codebucket_container}>
            <div className={classes.build_by_codebucket_content}>
                <motion.div style={{ x: moveLeft }} transition={transition}>
                    <GalaryPhoto src={Biprad} second_class={classes.image_two} />
                </motion.div>
                
                <motion.div style={{ x: moveRight }} transition={transition}>
                    <GalaryPhoto src={YtChampions} second_class={classes.image_one} />
                </motion.div>
                
                <motion.div style={{ x: moveRight }} transition={transition}>
                    <GalaryPhoto src={Prepca} second_class={classes.image_three} />
                </motion.div>
                
                <motion.div style={{ x: moveLeft }} transition={transition}>
                    <GalaryPhoto src={Bhavani} second_class={classes.image_four} />
                </motion.div>

                <div className={classes.cb_tag_wrapper} >
                    <motion.div style={{ y: moveDown }} transition={transition} className={classes.cb_tag}>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}cbWhite.webp`} width={100} height={100} alt="" className={classes.tag_image} />
                        <p className={classes.tag_text}> Build By Codebucket</p>
                    </motion.div>
                </div>
            </div>
        </div>
    );
};

export default BuildByCodebucket;
