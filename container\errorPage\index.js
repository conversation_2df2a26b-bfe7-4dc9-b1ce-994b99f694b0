import Image from "next/image";
import classes from "./styles.module.scss";
import Button from "@/components/button";
import { useRouter } from 'next/router';

const ErrorPage = () => {

    const router = useRouter();
    
    const handleBackClick = () => {
        router.back();
    };

    return(
        <div className={classes.error_page_container}>
            <div className={classes.error_page_content}>
                <div className={classes.left_content}>
                    <p className={classes.dialouge_text}>
                        Oops.... <br /> <span>page not found</span>
                    </p>

                    <p className={classes.hint_text}>
                        This Page doesn`t exist or was removed! <br />
                        We suggest you  back to home.
                    </p>
                    <Button 
                        button_text="Go Back"
                        variant="button_orange"
                        onClick={handleBackClick}
                    />
                </div>
                <Image className={classes.error_image} src={`${process.env.NEXT_PUBLIC_CDN_URL}error.webp`} width={700} height={700} alt="error image" />
            </div>
        </div>
    )
}

export default ErrorPage;