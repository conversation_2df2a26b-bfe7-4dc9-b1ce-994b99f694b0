@import "../../assets/css/global.scss";

.tab_container{
    width: 100%;
    height: auto;

    .tab_header, .tba_body{
        width: 100%;
        height: auto;

        .tab_button{
            all: unset;
        }
    }
}

.tab_one{
    .tab_header{
        display: flex;            
        align-items: center;
        justify-content: flex-start;
        gap: 40px;
        margin-bottom: 60px;

        .tab_button{
            font-size: 40px;
            font-family: $lexLight;
            color: rgb(68, 68, 68);
            cursor: pointer;
            transition: 0.3s ease-in-out;

            &:hover{
                color: white;
                color: rgb(122, 122, 122);
            }

            &.active{
                color: rgba(255, 255, 255, 0.5);  
            }
        }
    }
}

@media only screen and (max-width: 1366px){
    .tab_one{
        .tab_header{
            gap: 35px;
            margin-bottom: 50px;
    
            .tab_button{
                font-size: 35px;
            }
        }
    }
}

@media only screen and (max-width: 1180px){
    .tab_one{
        .tab_header{
            gap: 30px;
            margin-bottom: 40px;
    
            .tab_button{
                font-size: 30px;
            }
        }
    }
}

@media only screen and (max-width: 980px){
    .tab_one{
        .tab_header{
            gap: 25px;
            margin-bottom: 30px;
    
            .tab_button{
                font-size: 21px;
            }
        }
    }
}

@media only screen and (max-width: 667px){
    .tab_one{
        .tab_header{
            gap: unset;
            margin-bottom: 3.5vw;
            justify-content: space-between;
    
            .tab_button{
                font-size: 3.8vw;
            }
        }
    }
}