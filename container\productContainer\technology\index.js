import classes from "./styles.module.scss";
import Image from "next/image";
import Link from "next/link";
import { useAnimation } from "framer-motion";
import { useInView } from "react-intersection-observer";
import React from "react";

const Technology = () => {

    const controls = useAnimation();
    const [ref1, inView1] = useInView({ threshold: 0.5 });
    const [ref2, inView2] = useInView({ threshold: 0.1 });
    const [ref3, inView3] = useInView({ threshold: 0.5 });

    React.useEffect(() => {
        if (inView1) {
            controls.start("slideOne");
        } else if (inView2) {
            controls.start("slideTwo");
        } else if (inView3) {
            controls.start("slideThree");
        } else {
            controls.start("slideOne"); 
        }
    }, [inView1, inView2, inView3, controls]);

    const renderContent = () => {
        if (inView1) {
            return (
                <div className={classes.animation_content}>
                    <div className={classes.left_part}>
                        <div className={classes.head_desc_section}>
                            <p className={classes.head}>AI & ML</p>
                        </div>

                        <div className={classes.head_desc_section}>
                            <p className={classes.head} style={{color:"white"}}>Blockchain</p>
                            <p className={classes.desc} style={{color:"rgba(255, 255, 255, 0.50)"}}>
                              Transform your business with Blockchain today. Our blockchain experts develop custom solutions for supply chain, finance, and healthcare, ensuring trust, security, and efficiency. 
                            </p>
                        </div>

                        <div className={classes.head_desc_section}>
                            <p className={classes.head}>E-Commerce</p>
                        </div>
                    </div>

                    <div className={classes.right_part}>
                        <div className={classes.card_container}>
                            <div className={classes.card}>
                                <Image className={classes.card_image} src={`${process.env.NEXT_PUBLIC_CDN_URL}blockchain.webp`} width={300} height={300} alt="card-image" />
                                <Link href={''} className={classes.nav_header}>
                                    <p>Heading</p>
                                    <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}more.webp`} width={50} height={50} alt="icon" />
                                </Link>
                                <p className={classes.desc_text}>
                                    Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat
                                </p>
                            </div>
                        </div>
                        <div className={classes.card_container}>
                            <div className={classes.card}>
                                <Image className={classes.card_image} src={`${process.env.NEXT_PUBLIC_CDN_URL}blockchain.webp`} width={300} height={300} alt="card-image" />
                                <Link href={''} className={classes.nav_header}>
                                    <p>Heading</p>
                                    <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}more.webp`} width={50} height={50} alt="icon" />
                                </Link>
                                <p className={classes.desc_text}>
                                    Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat
                                </p>
                            </div>
                        </div>
                        <div className={classes.card_container}>
                            <div className={classes.card}>
                                <Image className={classes.card_image} src={`${process.env.NEXT_PUBLIC_CDN_URL}blockchain.webp`} width={300} height={300} alt="card-image" />
                                <Link href={''} className={classes.nav_header}>
                                    <p>Heading</p>
                                    <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}more.webp`} width={50} height={50} alt="icon" />
                                </Link>
                                <p className={classes.desc_text}>
                                    Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat
                                </p>
                            </div>
                        </div>
                        <div className={classes.card_container}>
                            <div className={classes.card}>
                                <Image className={classes.card_image} src={`${process.env.NEXT_PUBLIC_CDN_URL}blockchain.webp`} width={300} height={300} alt="card-image" />
                                <Link href={''} className={classes.nav_header}>
                                    <p>Heading</p>
                                    <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}more.webp`} width={50} height={50} alt="icon" />
                                </Link>
                                <p className={classes.desc_text}>
                                    Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            );
        } else if (inView2) {
            return (
                <div className={classes.animation_content}>
                    <div className={classes.animation_content}>
                        <div className={classes.left_part}>
                            <div className={classes.head_desc_section}>
                                <p className={classes.head}>AI & ML</p>
                            </div>

                            <div className={classes.head_desc_section}>
                                <p className={classes.head}>Blockchain</p>
                            </div>

                            <div className={classes.head_desc_section}>
                                <p className={classes.head} style={{color:"white"}}>E-Commerce</p>
                                <p className={classes.desc} style={{color:"rgba(255, 255, 255, 0.50)"}}>
                                  Our eCommerce solutions, including custom website design, mobile optimization, inventory management, and secure checkout integration for online success. 
                                </p>
                            </div>
                        </div>

                        <div className={classes.right_part}>
                            <div className={classes.card_container}>
                                <div className={classes.card}>
                                    <Image className={classes.card_image} src={`${process.env.NEXT_PUBLIC_CDN_URL}ecommerce.webp`} width={300} height={300} alt="card-image" />
                                    <Link href={''} className={classes.nav_header}>
                                        <p>Heading</p>
                                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}more.webp`} width={50} height={50} alt="icon" />
                                    </Link>
                                    <p className={classes.desc_text}>
                                        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat
                                    </p>
                                </div>
                            </div>
                            <div className={classes.card_container}>
                                <div className={classes.card}>
                                    <Image className={classes.card_image} src={`${process.env.NEXT_PUBLIC_CDN_URL}ecommerce.webp`} width={300} height={300} alt="card-image" />
                                    <Link href={''} className={classes.nav_header}>
                                        <p>Heading</p>
                                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}more.webp`} width={50} height={50} alt="icon" />
                                    </Link>
                                    <p className={classes.desc_text}>
                                        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat
                                    </p>
                                </div>
                            </div>
                            <div className={classes.card_container}>
                                <div className={classes.card}>
                                    <Image className={classes.card_image} src={`${process.env.NEXT_PUBLIC_CDN_URL}ecommerce.webp`} width={300} height={300} alt="card-image" />
                                    <Link href={''} className={classes.nav_header}>
                                        <p>Heading</p>
                                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}more.webp`} width={50} height={50} alt="icon" />
                                    </Link>
                                    <p className={classes.desc_text}>
                                        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat
                                    </p>
                                </div>
                            </div>
                            <div className={classes.card_container}>
                                <div className={classes.card}>
                                    <Image className={classes.card_image} src={`${process.env.NEXT_PUBLIC_CDN_URL}ecommerce.webp`} width={300} height={300} alt="card-image" />
                                    <Link href={''} className={classes.nav_header}>
                                        <p>Heading</p>
                                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}more.webp`} width={50} height={50} alt="icon" />
                                    </Link>
                                    <p className={classes.desc_text}>
                                        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            );
        } else {
            return (
                <div className={classes.animation_content}>
                    <div className={classes.animation_content}>
                        <div className={classes.left_part}>
                            <div className={classes.head_desc_section}>
                                <p className={classes.head} style={{color:"white"}}>AI & ML</p>
                                <p className={classes.desc} style={{color:"rgba(255, 255, 255, 0.50)"}}>
                                  Unlock business potential with AI/ML solutions—automation, predictive analytics, and deep learning for smarter decision-making and enhanced efficiency.  
                                </p>
                            </div>

                            <div className={classes.head_desc_section}>
                                <p className={classes.head}>Blockchain</p>
                            </div>

                            <div className={classes.head_desc_section}>
                                <p className={classes.head}>E-Commerce</p>
                            </div>
                        </div>

                        <div className={classes.right_part}>
                            <div className={classes.card_container}>
                                <div className={classes.card}>
                                    <Image className={classes.card_image} src={`${process.env.NEXT_PUBLIC_CDN_URL}aiml.webp`} width={300} height={300} alt="card-image" />
                                    <Link href={''} className={classes.nav_header}>
                                        <p>Heading</p>
                                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}more.webp`} width={50} height={50} alt="icon" />
                                    </Link>
                                    <p className={classes.desc_text}>
                                        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat
                                    </p>
                                </div>
                            </div>
                            <div className={classes.card_container}>
                                <div className={classes.card}>
                                    <Image className={classes.card_image} src={`${process.env.NEXT_PUBLIC_CDN_URL}aiml.webp`} width={300} height={300} alt="card-image" />
                                    <Link href={''} className={classes.nav_header}>
                                        <p>Heading</p>
                                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}more.webp`} width={50} height={50} alt="icon" />
                                    </Link>
                                    <p className={classes.desc_text}>
                                        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat
                                    </p>
                                </div>
                            </div>
                            <div className={classes.card_container}>
                                <div className={classes.card}>
                                    <Image className={classes.card_image} src={`${process.env.NEXT_PUBLIC_CDN_URL}aiml.webp`} width={300} height={300} alt="card-image" />
                                    <Link href={''} className={classes.nav_header}>
                                        <p>Heading</p>
                                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}more.webp`} width={50} height={50} alt="icon" />
                                    </Link>
                                    <p className={classes.desc_text}>
                                        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat
                                    </p>
                                </div>
                            </div>
                            <div className={classes.card_container}>
                                <div className={classes.card}>
                                    <Image className={classes.card_image} src={`${process.env.NEXT_PUBLIC_CDN_URL}aiml.webp`} width={300} height={300} alt="card-image" />
                                    <Link href={''} className={classes.nav_header}>
                                        <p>Heading</p>
                                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}more.webp`} width={50} height={50} alt="icon" />
                                    </Link>
                                    <p className={classes.desc_text}>
                                        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            );
        }
    };

    return(
        <div className={classes.technology_container}>
            <div className={classes.technology_content} style={{ height: "300vh" }}>
                <div className={classes.content}>
                    <div className={classes.framer_wrapper}>
                        {renderContent()}
                    </div>
                </div>

                <div ref={ref1} style={{ height: "100vh"}}></div>
                <div ref={ref2} style={{ height: "100vh"}}></div>
                <div ref={ref3} style={{ height: "100vh"}}></div>
            </div>
        </div>
    )
}

export default Technology;