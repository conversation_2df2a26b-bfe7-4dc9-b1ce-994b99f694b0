@import "../../assets/css/global.scss";

.input_types{
    width: 100%;
    height: auto;

    label{
        display: block;
        font-size: 18px;
        font-family: $lexLight;
        margin-bottom: 5px;
    }
    input{
        width: 100%;
        font-size: 16px;
        font-family: $lexLight;
        color: #848484;
        padding: 11px 20px;
        border-radius: 4px;
        border: 1px solid #848484;
        outline: none;

        &::placeholder{
            font-size: inherit;
            font-family: inherit;
            color: inherit;
        }
    }
}

@media only screen and (max-width: 1366px){
    .input_types{
        label{
            font-size: 16px;
            // margin-bottom: 5px;
        }
        input{
            font-size: 15px;
            padding: 9px 17px;
            // border-radius: 4px;
        }
    }   
}

@media only screen and (max-width: 1180px){
    .input_types{
        label{
            font-size: 15px;
            // margin-bottom: 5px;
        }
        input{
            font-size: 14px;
            // padding: 9px 17px;
            // border-radius: 4px;
        }
    }   
}

@media only screen and (max-width: 980px){
    .input_types{
        label{
            font-size: 14px;
            // margin-bottom: 5px;
        }
        input{
            font-size: 13px;
            padding: 8px 15px;
            // border-radius: 4px;
        }
    }   
}

@media only screen and (max-width: 780px){
    .input_types{
        label{
            font-size: 13px;
            // margin-bottom: 5px;
        }
        input{
            font-size: 12px;
            padding: 6px 12px;
            // border-radius: 4px;
        }
    }   
}