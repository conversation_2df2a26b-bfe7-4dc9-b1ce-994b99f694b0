@import "../../../assets/css/global.scss";

.satisfied_result{
    width: 100%;
    min-height: 100vh;
    padding: 80px 40px;
    background-color: white;
    border-radius: 30px;
    padding-bottom: 200px;

    .satisfied_result_header{
        font-size: 24px;
        font-family: $ubuntuRegular;
        margin-bottom: 35px;
    }

    .results_over_view_cards{
        width: 100%;
        height: auto;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 60px;

        .cards_wrapper{
            width: 26%;
            height: auto;
            aspect-ratio: 350/400;
            margin-left: -30px;
            display: flex;
            align-items: center;
            justify-content: center;
            
            &:nth-child(1) { z-index: 1; }
            &:nth-child(2) { z-index: 2; }
            &:nth-child(3) { z-index: 3; }
            &:nth-child(4) { z-index: 4; }

            .card{
                width: 100%;
                height: 100%;
                border: 1px solid rgba(0, 0, 0, 0.75);
                border-radius: 20px;
                background-color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;
                user-select: none;

                .number{
                    font-size: 72px;
                    font-family: $lexLight;
                    margin-bottom: 20px;
                    color: black;
                }

                .text{
                    font-size: 24px;
                    font-family: $lexRegular;
                    color: black;
                }

                &:hover{
                    border: unset;
                    background: linear-gradient(166.36deg, #FF9900 -48.27%, #990000 245.53%);
                    transform: scale(0.98);
                    transition: 200ms;

                    .number, .text{
                        color: white;
                        font-family: $lexMedium;

                        span{
                            color: white;
                        }
                    }
                }
            }
        }

        .cards_wrapper:first-child{
            margin-left: unset;
        }
    }

    .re_exp_header{
        display: flex;
        align-items: flex-end;
        justify-content: space-between;
        margin-bottom: 40px;
    }

    .expertise_header{
        font-size: 36px;
        font-family: $ubuntuRegular;
        
        span{
            color: #FC9700;
        }
    }

    .some_text{
        font-size: 14px;
        font-family: $lexLight;
        margin-bottom: 3px;
        text-align: right;
    }

    .company_names{
        width: 100%;
        height: auto;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        border: 1px solid #00000040;

        .company_name_cards{
            width: 25%;
            height: auto;
            aspect-ratio: 225/130;
            border: 1px solid #00000040;
            display: flex;
            align-items: center;
            justify-content: center;

            img{
                width: 100%;
                height: 100%;
                object-fit: contain;
                filter: grayscale(100%);                

                &:hover{
                    filter: grayscale(0%);
                    transition: 200ms;
                }
            }

            &:nth-child(1){
                padding: 0px 60px;
            }
        }
    }
}

@media only screen and (max-width: 1366px){
    .satisfied_result{
        padding: 72px 36px;
    
        .satisfied_result_header{
            font-size: 22px;
            margin-bottom: 30px;
        }
    
        .results_over_view_cards{
            margin-bottom: 50px;
    
            .cards_wrapper{
    
                .card{
                    border-radius: 18px;
    
                    .number{
                        font-size: 60px;
                        margin-bottom: 15px;
                    }
    
                    .text{
                        font-size: 21px;
                    }
                }
            }
        }

        .re_exp_header{
            margin-bottom: 35px;
        }
    
        .expertise_header{
            font-size: 32px;
        }
    }
}

@media only screen and (max-width: 1180px){
    .satisfied_result{
        padding: 48px 24px;
    
        .header{
            .satisfied_result_header{
                font-size: 20px;
                margin-bottom: 25px;
            }
        }
    
        .results_over_view_cards{
            margin-bottom: 40px;
    
            .cards_wrapper{
                .card{
                    .number{
                        font-size: 50px;
                        margin-bottom: 10px;
                    }
    
                    .text{
                        font-size: 19px;
                    }
                }
            }
        }

        .re_exp_header{
            margin-bottom: 30px;
        }
    
        .expertise_header{
            font-size: 28px;
        }
    }
}

@media only screen and (max-width: 1024px){
    .satisfied_result{
        min-height: 100dvh;
        padding: 80px 40px;
        padding-bottom: 200px;
    
        .satisfied_result_header{
            font-size: 4.5vw;
            margin-bottom: 6vw;
        }
    
        .results_over_view_cards{
            margin-bottom: 6vw;
    
            .cards_wrapper{
                .card{
                    .number{
                        font-size: 6vw;
                        margin-bottom: 2vw;
                    }
    
                    .text{
                        font-size: 2.4vw;
                    }
                }
            }
        }
    
        .re_exp_header{
            margin-bottom: 4vw;
        }
    
        .expertise_header{
            font-size: 4vw;
        }
    
        .some_text{
            font-size: 2vw;
        }
    
        .company_names{
            flex-wrap: wrap;
    
            .company_name_cards{
                width: 50%;
            }
        }
    }
}

@media only screen and (max-width: 667px){
    .satisfied_result{
        padding: 40px 15px 100px;
    
        .satisfied_result_header{
            font-size: 5vw;
            margin-bottom: 6vw;
        }
    
        .results_over_view_cards{
            margin-bottom: 10vw;
            flex-wrap: wrap;
            gap: 4vw;
    
            .cards_wrapper{
                width: calc(50% - 2vw);
                margin-left: unset;
                aspect-ratio: 2/1.8;

                .card{
                    .number{
                        font-size: 8vw;
                        margin-bottom: 2vw;
                    }
    
                    .text{
                        font-size: 3vw;
                    }
                }
            }
        }
    
        .re_exp_header{
            margin-bottom: 4vw;
        }
    
        .expertise_header{
            font-size: 5vw;
        }
    
        .some_text{
            font-size: 2.8vw;
        }
    
        .company_names{
            flex-wrap: wrap;
    
            .company_name_cards{
                width: 50%;

                &:nth-child(1){
                    padding: 0px 30px;
                }
            }
        }
    }
}