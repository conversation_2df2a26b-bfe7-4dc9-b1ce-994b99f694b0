@import "../../assets/css/global.scss";

.error_page_container{
    width: 100%;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;

    .error_page_content{
        width: 100%;
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 100px;
        margin-top: -15vh;
        margin-right: -7vw;

        .left_content{
            .dialouge_text{
                color: #CE4B1C;
                font-family: $lexMedium;
                font-size: 36px;
                text-transform: uppercase;
                margin-bottom: 10px;
    
                span{
                    font-size: 30px;
                }
            }
    
            .hint_text{
                color: #4B4B4B;
                font-family: $lexRegular;
                margin-bottom: 20px;
                font-size: 16px;
            }
        }
    
        .error_image{
            filter: grayscale(100%);
            object-fit: contain;
            width: 40%;
            height: auto;
        }
    }
}

@media only screen and (max-width: 1440px){
    .error_page_container{
        .error_page_content{
            .error_image{
                width: 50%;
            }
        }
    }
}

@media only screen and (max-width: 1280px){
    .error_page_container{
        .error_page_content{
            .left_content{
                .dialouge_text{
                    font-size: 30px;
                    span{
                        font-size: 26px;
                    }
                }
                .hint_text{
                    font-size: 14px;
                    margin-bottom: 15px;
                }

            }

            .error_image{
                width: 40%;
            }
        }
    }
}

@media only screen and (max-width: 800px){
    .error_page_container{
        align-items: start;

        .error_page_content{
            flex-direction: column-reverse;
            justify-content: flex-end;
            margin-top: 10vw;
            margin-right: unset;
            gap: 4vw;

            .left_content{
                .dialouge_text{
                    font-size: 4vw;
                    margin-bottom: 1.5vw;
                    
                    span{
                        font-size: 3.6vw;
                    }
                }
                .hint_text{
                    font-size: 2.4vw;
                    margin-bottom: 2vw;
                }

                button{
                    padding: 10px 20px;
                    font-size: 2.6vw;
                }

            }

            .error_image{
                width: 50%;
            }
        }
    }
}

@media only screen and (max-width: 667px){
    .error_page_container{
        align-items: start;

        .error_page_content{
            margin-top: 10vh;
            gap: unset;

            .left_content{
                .dialouge_text{
                    text-align: center;
                    font-size: 8vw;
                    margin-bottom: 2vw;
                    line-height: 8vw;
                    
                    span{
                        font-size: 5vw;
                    }
                }
                .hint_text{
                    font-size: 3.4vw;
                    margin-bottom: 5vw;
                    text-align: center;
                }

                button{
                    padding: 10px 20px;
                    font-size: 3.4vw;
                    position: relative;
                    left: 50%;
                    transform: translate(-50%);
                }

            }

            .error_image{
                width: 65%;
            }
        }
    }
}