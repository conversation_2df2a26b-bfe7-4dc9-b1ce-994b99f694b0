@import "../../assets/css/global.scss";

.normal_button{
    border: none;
    background-color: transparent;
    font-family: $lexLight;
    font-size: $font16;
    height: auto;
    padding: 12px 28px;
    border-radius: 50px;
    color: white;
    cursor: pointer;

    &:hover{
        opacity: 0.8;
    }
}

.button_black{
    background-color: black;
}

.button_orange{
    background: linear-gradient(166.36deg, #FF9900 -48.27%, #990000 245.53%);
}

@media only screen and (max-width: 1366px){
    .normal_button{
        padding: 11px 24px;
        font-size: 15px;
    }
}

@media only screen and (max-width: 1180px){
    .normal_button{
        padding: 10px 20px;
        font-size: 14px;
    }
}

@media only screen and (max-width: 980px){
    .normal_button{
        padding: 8px 17px;
        font-size: 13px;
    }
}

@media only screen and (max-width: 780px){
    .normal_button{
        padding: 6px 13px;
        font-size: 13px;
    }
}

@media only screen and (max-width: 667px){
    .normal_button{
        height: auto;
        width: auto;
        padding: 10px 20px;
        font-size: 3.5vw;
    }
}