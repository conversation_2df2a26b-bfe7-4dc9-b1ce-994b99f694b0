import { useState, useEffect, useRef } from 'react';

const MyComponent = () => {
  const [textColor, setTextColor] = useState('black'); // Default text color
  const paragraphRef = useRef(null);

  // Function to handle scroll event
  const onScroll = () => {
    const paragraphRect = paragraphRef.current.getBoundingClientRect();

    // Find the section that the paragraph is currently over
    const sectionElements = document.querySelectorAll('div'); // Directly select all divs that might be sections

    sectionElements.forEach((section) => {
      const sectionRect = section.getBoundingClientRect();

      // Check if the sticky paragraph is over this section
      if (
        paragraphRect.top >= sectionRect.top &&
        paragraphRect.top < sectionRect.bottom
      ) {
        // Get the background color of the section
        const bgColor = window.getComputedStyle(section).backgroundColor;

        // Check if the background is dark or light and adjust text color accordingly
        const rgb = bgColor.match(/\d+/g); // Extract RGB values
        const [r, g, b] = rgb.map(Number);
        const brightness = 0.2126 * r + 0.7152 * g + 0.0722 * b;

        if (brightness < 128) {
          setTextColor('white'); // Dark background, use white text
        } else {
          setTextColor('black'); // Light background, use black text
        }
      }
    });
  };

  useEffect(() => {
    // Attach the scroll event listener
    window.addEventListener('scroll', onScroll);

    // Initial check in case the page is already scrolled
    onScroll();

    // Cleanup the event listener on component unmount
    return () => {
      window.removeEventListener('scroll', onScroll);
    };
  }, []);

  return (
    <div>
      {/* Sticky paragraph */}
      <p
        ref={paragraphRef}
        style={{
          position: 'fixed',
          top: '50px',
          left: '50%',
          transform: 'translateX(-50%)',
          fontSize: '24px',
          color: textColor, // Dynamic text color
        }}
      >
        This paragraph changes color based on background!
      </p>

      {/* Sections with different background colors */}
      <div style={{ height: '100vh', backgroundColor: 'yellow' }}>
        <h2>Section 1 (Yellow)</h2>
      </div>
      <div style={{ height: '100vh', backgroundColor: 'darkgreen' }}>
        <h2>Section 2 (Dark Green)</h2>
      </div>
      <div style={{ height: '100vh', backgroundColor: 'orange' }}>
        <h2>Section 3 (Orange)</h2>
      </div>
      <div style={{ height: '100vh', backgroundColor: 'black' }}>
        <h2>Section 4 (Black)</h2>
      </div>
      <div style={{ height: '100vh', backgroundColor: 'green' }}>
        <h2>Section 5 (Green)</h2>
      </div>
    </div>
  );
};

export default MyComponent;
