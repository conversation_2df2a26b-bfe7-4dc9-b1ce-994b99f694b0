import classes from "./styles.module.scss";
import Image from "next/image";
import Button from "../../../components/button";
import OverallCard from "../../../components/overallCard";
import CountUp from 'react-countup';
import Cmmi2 from "../../../assets/images/cmmi2.webp";

import { motion } from "framer-motion";
import { useState } from "react";


const JoinATeam = () => {

    const [position, setPosition] = useState({ x: "50%", y: "50%" });
    const [isHovering, setIsHovering] = useState(false);

    const handleMouseMove = (e) => {
        const { left, top, width, height } = e.currentTarget.getBoundingClientRect();
        const x = `${((e.clientX - left) / width) * 100}%`;
        const y = `${((e.clientY - top) / height) * 100}%`;
        setPosition({ x, y });
        setIsHovering(true);
    };

    const handleMouseLeave = () => {
        setIsHovering(false);
    };

    return(
        <div className={classes.join_a_team}>
            <div className={classes.join_a_team_header}>
                <div className={classes.header_left_part}>
                    <div className={classes.head}>
                        Join a team that <br /> Inspires&nbsp; 
                        <p className={classes.container} onMouseMove={handleMouseMove} onMouseLeave={handleMouseLeave}>
                            <motion.span
                                className={classes.torch_text}
                                animate={{ "--x": position.x, "--y": position.y, opacity: isHovering ? 1 : 0.3 }}
                                initial={{ opacity: 0.2 }}
                                transition={{ opacity: { duration: 0.4, ease: "easeInOut" }, "--x": { duration: 0.3 }, "--y": { duration: 0.3 } }}
                            >
                                and Create
                            </motion.span>
                        </p>
                    </div>

                    <p className={classes.desc_text}>We have a strong desire to assemble a group of gifted people who are committed to our mission. Look through our available jobs and see how you can contribute.</p>
                    <div className={classes.hr}></div>
                    <p className={classes.desc_text_orange}>More than just a job, we offer a culture of learning, growth, and meaningful work that makes a difference.</p>
                    <Button 
                        button_text="See Openings" 
                        icon_two={<Image src={`${process.env.NEXT_PUBLIC_CDN_URL}Right.webp`} width={33} height={20} alt="icon"/>} 
                        variant="button_orange"
                        second_class={classes.see_openings}
                        onClick={() => window.open('https://jobs.codebuckets.in', '_blank', 'noopener,noreferrer')}
                    />   
                </div>
                <Image className={classes.right_banner} src={`${process.env.NEXT_PUBLIC_CDN_URL}sports.webp`} width={856} height={484} alt="banner" />
            </div>
            <div className={classes.status_of_company}>
                <div className={classes.overall_status}>
                    <OverallCard 
                        count= { <><CountUp end={250} start={0} />+</> }
                        text="Employees" 
                    />
                    <OverallCard 
                        count= { <><CountUp end={3} start={0} />+</> }
                        text="Offices" 
                    />
                    <OverallCard 
                        count= { <><CountUp end={10} start={0} />+</> }
                        text="Trusted By" 
                    />
                    <OverallCard 
                        count= { <><CountUp end={1500} start={0} />+</> }
                        text="Delivered Projects" 
                    />
                    <OverallCard 
                        count= { <><CountUp end={7} start={0} />+</> }
                        text="Years in Business" 
                    />
                </div>
                <div className={classes.audit_status}>
                     <div className={classes.audit_wrapper}>
                        <Image src={Cmmi2} width={420} height={275} alt="company" />
                    </div>
                     <div className={classes.audit_wrapper}>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}iso.webp`} width={420} height={275} alt="company" />
                    </div>
                    <div className={classes.audit_wrapper}>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}kenoics.webp`} width={420} height={275} alt="company" />
                    </div>
                    <div className={classes.audit_wrapper}>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}symbol.webp`} width={420} height={275} alt="company" />
                    </div>
                </div>
            </div>
        </div>
    )
}

export default JoinATeam;