import { useState, useEffect, useRef } from "react";
import classes from "./styles.module.scss";

const ColorHam = () => {
  const [firstDivColor, setFirstDivColor] = useState('white');
  const [secondDivColor, setSecondDivColor] = useState('white');
  const [thirdDivColor, setThirdDivColor] = useState('white');
  
  const firstDivRef = useRef(null);
  const secondDivRef = useRef(null);
  const thirdDivRef = useRef(null);

  const onScroll = () => {
    const divs = [firstDivRef, secondDivRef, thirdDivRef];
    
    divs.forEach((ref, index) => {
      if (ref.current) {
        const divRect = ref.current.getBoundingClientRect();
        const sections = document.querySelectorAll('div');
        sections.forEach((section) => {
          const sectionRect = section.getBoundingClientRect();

          if (
            divRect.top >= sectionRect.top &&
            divRect.top < sectionRect.bottom
          ) {
            const bgColor = window.getComputedStyle(section).backgroundColor;
            const rgb = bgColor.match(/\d+/g);
            const [r, g, b] = rgb.map(Number);
            const brightness = 0.2126 * r + 0.7152 * g + 0.0722 * b;

            if (brightness < 128) {
              if (index === 0) setFirstDivColor('white');
              if (index === 1) setSecondDivColor('white');
              if (index === 2) setThirdDivColor('white');
            } else {
              if (index === 0) setFirstDivColor('black');
              if (index === 1) setSecondDivColor('black');
              if (index === 2) setThirdDivColor('black');
            }
          }
        });
      }
    });
  };

  useEffect(() => {
    window.addEventListener('scroll', onScroll);
    onScroll();

    return () => {
      window.removeEventListener('scroll', onScroll);
    };
  }, []);

  return (
    <div className={classes.ham_container}>
      <div
        ref={firstDivRef}
        style={{ backgroundColor: firstDivColor }}
        className={classes.bar}
      ></div>
      <div
        ref={secondDivRef}
        style={{ backgroundColor: secondDivColor }}
        className={classes.bar}
      ></div>
      <div
        ref={thirdDivRef}
        style={{ backgroundColor: thirdDivColor }}
        className={classes.bar}
      ></div>
    </div>
  );
};

export default ColorHam;