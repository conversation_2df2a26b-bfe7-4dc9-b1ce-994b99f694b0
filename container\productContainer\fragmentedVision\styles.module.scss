@import "../../../assets/css/global.scss";

.fragmented_vision_container{
    width: 100%;
    min-height: 100vh;
    background-color: white;
    position: relative;

    .bg_image{
        position: absolute;
        width: 100%;
        height: 100%;
        background-image: url('https://prod-1.static.codebuckets.in/file/codebucket-production-public/codebucket-official-website/Ellipse%20215.webp'), url('https://prod-1.static.codebuckets.in/file/codebucket-production-public/codebucket-official-website/Ellipse%2021611.webp');
        background-repeat: no-repeat;
        background-position: bottom left, top right;
        background-size: 35%, 40%;
        z-index: 2;
    }

    .half_bg_black{
        height: 68%;
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        border-radius: 30px 30px 0px 0px;
        background-color: transparent;
        transition: 300ms ease-in;

        &.active{
            background-color: black;
        }
    }

    .fragmented_vision_content{
        width: 100%;
        height: auto;
        padding: 80px 40px 40px;
        background-color: white;

        .fragment_vision_content_header{
            width: 100%;
            height: auto;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;

            .left{
                width: 60%;
                margin-right: 50px;
                background-color: white;

                .left_header{
                    font-size: 60px;
                    font-family: $ubuntuRegular;
                    margin-bottom: 20px;
                }

                .left_desc{
                    font-size: 20px;
                    font-family: $lexLight;
                    display: flex;
                }
            }
            .right{
                width: 40%;
                display: flex;
                flex-wrap: wrap;
                gap: 10px;
                padding-top: 20px;
                background-color: white;

                .right_card{
                    background-color: white;
                    border-radius: 50px;
                    font-size: 20px;
                    padding: 10px 20px 12px;
                    white-space: nowrap;
                    z-index: 99;
                    font-family: $lexRegular;
                }
            }
        }

        .photo_galary_content{
            width: 100%;
            height: 640px;
            position: relative;
            margin-top: 140px;
            transition: 400ms ease-in-out;

            &.active{
                margin-top: 90px;
            }

            .img_one, .img_two, .img_three{
                position: absolute;
                opacity: 0.85;
                transition: 100ms ease-in-out;
                
                &.active{
                    opacity: 1;
                }
            }

            .img_one{
                width: 50%;
                left: 30px;
                top: 20px;
                z-index: 2;
            }

             .img_three{
                width: 34%;
                right: 0px;
                top: 160px;
                z-index: 2;
            }

            .img_two{
                width: 20%;
                left: 55%;
                top: 40px;
                z-index: 2;
            }
        }
    }
}

@media only screen and (max-width: 1366px){
    .fragmented_vision_container{
        .half_bg_black{
            height: 66%;
        }
    
        .fragmented_vision_content{
            .fragment_vision_content_header{
                .left{
                    .left_header{
                        font-size: 52px;
                        margin-bottom: 20px;
                    }
    
                    .left_desc{
                        font-size: 18px;
                    }
                }
                .right{
                    .right_card{
                        font-size: 18px;
                    }
                }
            }
    
            .photo_galary_content{
                height: 540px;
                margin-top: 120px;
    
                &.active{
                    margin-top: 70px;
                }
    
                .img_one, .img_two, .img_three{
                    position: absolute;
                    opacity: 0.85;
                    transition: 300ms ease-in-out;
                    
                    &.active{
                        opacity: 1;
                    }
                }
    
                .img_one{
                    left: 30px;
                    top: 20px;
                }
    
                .img_two{
                    left: 55%;
                    top: 40px;
                }
    
                .img_three{
                    right: 0px;
                    top: 160px;
                }
            }
        }
    }
}

@media only screen and (max-width: 1280px){
    .fragmented_vision_container{
        .half_bg_black{
            height: 66%;
        }
    
        .fragmented_vision_content{
            .fragment_vision_content_header{
                .left{
                    .left_header{
                        font-size: 46px;
                        margin-bottom: 20px;
                    }
    
                    .left_desc{
                        font-size: 16px;
                    }
                }
                .right{
                    .right_card{
                        font-size: 16px;
                        padding: 8px 16px;
                    }
                }
            }
    
            .photo_galary_content{
                height: 480px;
                margin-top: 100px;
    
                &.active{
                    margin-top: 50px;
                }
            }
        }
    }
}

@media only screen and (max-width: 1080px){
    .fragmented_vision_container{
        .half_bg_black{
            height: 66%;
        }
    
        .fragmented_vision_content{
    
            .fragment_vision_content_header{
    
                .left{
    
                    .left_header{
                        font-size: 38px;
                        margin-bottom: 15px;
                    }
    
                    .left_desc{
                        font-size: 14px;
                    }
                }
                .right{
                    .right_card{
                        font-size: 14px;
                        padding: 8px 16px;
                    }
                }
            }
    
            .photo_galary_content{
                height: 440px;
                margin-top: 70px;
    
                &.active{
                    margin-top: 50px;
                }
            }
        }
    }
}

@media only screen and (max-width: 667px){
    .fragmented_vision_container{
        width: 100%;
        min-height: auto;

        .bg_image{
            background-size: 35%, 90%;
            z-index: 2;
        }

        .half_bg_black{
            height: 60%;
            z-index: 2;
        }

        .fragmented_vision_content{
            padding: 30px 15px;
            position: relative;

            .fragment_vision_content_header{
                display: block;

                .left{
                    width: 100%;
                    margin-right: unset;
                    margin-bottom: 5vw;

                    .left_header{
                        font-size: 8vw;
                        margin-bottom: 3vw;
                        position: relative;
                        z-index: 1s;

                        br{
                            display: none;
                        }
                    }

                    .left_desc{
                        font-size: 3.7vw;
                        position: relative;
                        z-index: 5;
                    }
                }
                .right{
                    width: 100%;
                    gap: 10px;
                    padding-top: unset;

                    .right_card{
                        border-radius: 50px;
                        border: 1px solid grey;
                        font-size: 3.5vw;
                        padding: 1vw 3vw 1.3vw;
                        position: relative;
                    }
                }
            }

            .photo_galary_content{
                width: 100%;
                height: 120vw;
                margin-top: 50px;
                transition: 400ms ease-in-out;

                &.active{
                    margin-top: 10px;
                }

                .img_one, .img_two, .img_three{
                    position: absolute;
                    opacity: 0.85;
                    transition: 100ms ease-in-out;
                    
                    &.active{
                        opacity: 1;
                    }
                }

                .img_one{
                    width: 70%;
                    left: 13vw;
                    top: 10vw;
                    z-index: 3;
                }

                .img_two{
                    width: 60%;
                    left: unset;
                    right: 0px;
                    top: 50vw;
                    z-index: 2;
                }

                .img_three{
                    width: 60%;
                    right: unset;
                    left: 4vw;
                    top: 80vw;
                    z-index: 2;
                }
            }
        }
    }
}