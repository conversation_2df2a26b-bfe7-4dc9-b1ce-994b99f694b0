import { useState, useEffect } from "react";
import classes from "./styles.module.scss";
import Image from "next/image";
import Link from "next/link";
import Input from "../../components/inputTypes";
import CustomSelect from "../../components/customSelect";
import Textarea from "../../components/textarea";
import Button from "../../components/button";
import LocationSection from "./locationSection";

const isValidEmail = (email) => {
  if (email.length > 320) return false;
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return emailRegex.test(email);
};

const isValidPhoneNumber = (phone) => {
  const phoneRegex = /^\d{10}$/;
  return phoneRegex.test(phone);
};

const ContactUsContainer = () => {
  const [form, setForm] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phoneNumber: "",
    serviceLookingFor: "",
    message: "",
  });

  const [services, setServices] = useState([]);
  const [contactLoading, setContactLoading] = useState(false);
  const [contactSubmitted, setContactSubmitted] = useState(false);

  const [newsletterEmail, setNewsletterEmail] = useState("");
  const [newsletterLoading, setNewsletterLoading] = useState(false);
  const [newsletterSubmitted, setNewsletterSubmitted] = useState(false);

  useEffect(() => {
    const fetchServices = async () => {
      try {
        const res = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/user-service`
        );
        const json = await res.json();
        if (!res.ok) throw new Error(json.message || `HTTP ${res.status}`);

        let servicesData = json?.data?.data || [];

        const serviceWithId9 = servicesData.find((service) => service.id === 9);
        const otherServices = servicesData.filter(
          (service) => service.id !== 9
        );

        const serviceOptions = otherServices.map((service) => ({
          label: service.name,
          value: service.name,
        }));

        if (serviceWithId9) {
          serviceOptions.push({
            label: serviceWithId9.name,
            value: serviceWithId9.name,
          });
        }

        setServices(serviceOptions);
      } catch (err) {
        console.error("Failed to fetch services:", err);
      }
    };

    fetchServices();
  }, []);

  const handleContactChange = (e) => {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleServiceSelect = (e) => {
    setForm((prev) => ({ ...prev, serviceLookingFor: e.target.value }));
  };

  const handleNewsletterChange = (e) => {
    setNewsletterEmail(e.target.value);
  };

  const handleContactSubmit = async (e) => {
    e.preventDefault();

    if (!isValidEmail(form.email)) {
      alert("Please enter a valid email address.");
      return;
    }

    if (!isValidPhoneNumber(form.phoneNumber)) {
      alert("Please enter a valid 10-digit phone number.");
      return;
    }

    setContactLoading(true);
    setContactSubmitted(false);

    try {
      const res = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/user-enquiry`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(form),
        }
      );

      const json = await res.json();
      if (!res.ok) throw new Error(json.message || `HTTP ${res.status}`);

      const payload = json.data?.data;
      if (!payload) throw new Error("Unexpected response structure");

      setForm({
        firstName: "",
        lastName: "",
        email: "",
        phoneNumber: "",
        serviceLookingFor: "",
        message: "",
      });

      setContactSubmitted(true);
    } catch (err) {
      console.error("Contact submission error:", err.message);
    } finally {
      setContactLoading(false);
    }
  };

  const handleNewsletterSubmit = async (e) => {
    e.preventDefault();

    if (!isValidEmail(newsletterEmail)) {
      alert("Please enter a valid email address.");
      return;
    }

    setNewsletterLoading(true);
    setNewsletterSubmitted(false);

    try {
      const res = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/newsletter`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ email: newsletterEmail }),
        }
      );

      const json = await res.json();
      if (!res.ok)
        throw new Error(json?.data?.message || "Subscription failed");

      setNewsletterEmail("");
      setNewsletterSubmitted(true);
    } catch (err) {
      console.error("Newsletter subscription error:", err.message);
    } finally {
      setNewsletterLoading(false);
    }
  };

   // Extract nested ternary for contact button text
  let contactButtonText;
  if (contactSubmitted) {
    contactButtonText = "Message Sent Successfully";
  } else if (contactLoading) {
    contactButtonText = "Submitting...";
  } else {
    contactButtonText = "Send Message";
  }

  // Extract nested ternary for newsletter button text
  let newsletterButtonText;
  if (newsletterSubmitted) {
    newsletterButtonText = "Subscribed Successfully";
  } else if (newsletterLoading) {
    newsletterButtonText = "Submitting...";
  } else {
    newsletterButtonText = "Send Message";
  }

  return (
    <div className={classes.contact_us_container}>
      <div className={classes.contact_us_content}>
        <div className={classes.header}>
          <p>Contact Us</p>
          <Image
            src={`${process.env.NEXT_PUBLIC_CDN_URL}arrowDown.webp`}
            width={130}
            height={130}
            alt="arrow"
          />
        </div>

        <div className={classes.content}>
          <div className={classes.left_form_content}>
            <form onSubmit={handleContactSubmit}>
              <div className={classes.line}>
                <Input
                  label="First Name"
                  type="text"
                  name="firstName"
                  placeholder="First Name"
                  value={form.firstName}
                  onChange={handleContactChange}
                  required
                />
                <Input
                  label="Last Name"
                  type="text"
                  name="lastName"
                  placeholder="Last Name"
                  value={form.lastName}
                  onChange={handleContactChange}
                  required
                />
              </div>

              <div className={classes.line}>
                <Input
                  label="Email"
                  type="email"
                  name="email"
                  placeholder="Enter"
                  value={form.email}
                  onChange={handleContactChange}
                  required
                />
              </div>

              <div className={classes.line}>
                <Input
                  label="Phone Number"
                  type="tel"
                  name="phoneNumber"
                  pattern="\d{10}"
                  maxLength={10}
                  inputMode="numeric"
                  placeholder="Enter 10digit mobile number"
                  value={form.phoneNumber}
                  onChange={handleContactChange}
                  required
                  onKeyDown={(e) => {
                    const allowedKeys = [
                      "Backspace",
                      "Delete",
                      "ArrowLeft",
                      "ArrowRight",
                      "Tab",
                    ];
                    const isNumberKey = /^\d$/.test(e.key);
                    if (!isNumberKey && !allowedKeys.includes(e.key)) {
                      e.preventDefault();
                    }
                  }}
                />
                <CustomSelect
                  label="Services Looking for"
                  options={services}
                  defaultOption="Select"
                  value={form.serviceLookingFor}
                  onChange={handleServiceSelect}
                  name="serviceLookingFor"
                  required
                />
              </div>

              <div className={classes.line}>
                <Textarea
                  label="Your Message"
                  name="message"
                  placeholder="Write your message here"
                  value={form.message}
                  onChange={handleContactChange}
                  required
                />
              </div>

              <Button
                button_text={contactButtonText}
                variant="button_orange"
                icon_two={
                  <Image
                    src={`${process.env.NEXT_PUBLIC_CDN_URL}Right.webp`}
                    alt="icon"
                    width={30}
                    height={20}
                  />
                }
                second_class={`${classes.msg_button} ${
                  contactSubmitted ? classes.msg_button_sent : ""
                }`}
                type="submit"
                disabled={contactLoading}
              />
            </form>
          </div>

          <div className={classes.right_form_content}>
            <p className={classes.right_header}>Join Our Newsletter</p>
            <p className={classes.desc_text}>
              Add your email to receive weekly including the offers and benefits
            </p>

            <form onSubmit={handleNewsletterSubmit}>
              <div className={classes.line}>
                <Input
                  type="email"
                  label="Email"
                  name="newsletterEmail"
                  placeholder="Enter your email"
                  value={newsletterEmail}
                  onChange={handleNewsletterChange}
                  required
                />
              </div>

              <Button
                button_text={newsletterButtonText}
                variant="button_orange"
                icon_two={
                  <Image
                    src={`${process.env.NEXT_PUBLIC_CDN_URL}Right.webp`}
                    alt="icon"
                    width={30}
                    height={20}
                  />
                }
                second_class={`${classes.msg_button} ${
                  newsletterSubmitted ? classes.msg_button_sent : ""
                }`}
                type="submit"
                disabled={newsletterLoading}
              />
            </form>

            <p className={classes.contact_us_header}>Contact With Us</p>
            <p className={classes.main}><EMAIL></p>
            <p className={classes.phone_no}>+91 99950 08671</p>

            <div className={classes.social_links}>
              <Link
                href={"https://www.facebook.com/codebuckets"}
                target="_blank"
              >
                <Image
                  src={`${process.env.NEXT_PUBLIC_CDN_URL}blackFB.webp`}
                  width={40}
                  height={40}
                  alt="icon"
                />
              </Link>
              <Link
                href={"https://www.instagram.com/codebuckets_/"}
                target="_blank"
              >
                <Image
                  src={`${process.env.NEXT_PUBLIC_CDN_URL}blackInstagram.webp`}
                  width={40}
                  height={40}
                  alt="icon"
                />
              </Link>
              <Link href={"https://x.com/codebuckets_"} target="_blank">
                <Image
                  src={`${process.env.NEXT_PUBLIC_CDN_URL}blackTwitter.webp`}
                  width={40}
                  height={40}
                  alt="icon"
                />
              </Link>
              <Link
                href={"https://www.linkedin.com/company/13441306"}
                target="_blank"
              >
                <Image
                  src={`${process.env.NEXT_PUBLIC_CDN_URL}blackLinkedIn.webp`}
                  width={40}
                  height={40}
                  alt="icon"
                />
              </Link>
            </div>
          </div>
        </div>
        <LocationSection />
      </div>
    </div>
  );
};

export default ContactUsContainer;