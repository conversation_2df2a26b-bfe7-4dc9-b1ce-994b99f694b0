@import "../../../assets/css/global.scss";

.unlock_growth {
  width: 100%;
  min-height: 100vh;
  padding: 80px 40px;
  background-color: white;
  position: relative;
  overflow: hidden;

  .eclipse_one {
    position: absolute;
    top: 0vh;
    width: 60vw;
    height: auto;
    background-color: white;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  .main_header {
    font-size: 60px;
    line-height: 72px;
    font-family: $ubuntuRegular;
    margin: 25px 0px;
    position: relative;
    z-index: 9;

    span {
      background: linear-gradient(34deg, #990000 0%, #ff9900 60%);
      -webkit-background-clip: text;
      -moz-background-clip: text;
      -ms-background-clip: text;
      -o-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .desc_text {
    font-size: 20px;
    line-height: 28px;
    font-family: $lexLight;
    margin-bottom: 50px;
    position: relative;
  }

  .view_products_button {
    position: relative;
  }

  .photo_gallery {
    width: 100%;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    position: relative;

    .photo_container {
      width: calc(25% - 15px);
      aspect-ratio: 345/333;
      border-radius: 0px 45px 0px 45px;
      overflow: hidden;

      .img_container {
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 200ms ease-in-out;

          &:hover {
            transform: scale(1.03);
          }
        }
      }
    }

    .photo_container:nth-child(1) {
      display: flex;
      justify-content: space-between;
      flex-direction: column;
      gap: 10px;

      .img_container:nth-child(1),
      .img_container:nth-child(2) {
        width: 100%;
      }

      .img_container:nth-child(1) {
        height: 42%;
      }

      .img_container:nth-child(2) {
        height: 58%;
      }
    }

    .photo_container:nth-child(4) {
      display: flex;
      justify-content: space-between;
      flex-direction: column;
      gap: 10px;

      .img_container:nth-child(1),
      .img_container:nth-child(2) {
        object-fit: cover;
        width: 100%;
      }

      .img_container:nth-child(2) {
        height: 42%;
      }

      .img_container:nth-child(1) {
        height: 58%;
      }
    }
  }
}

@media only screen and (max-width: 1440px) {
  .unlock_growth {
    .eclipse_one {
      top: 8vw;
    }
  }
}

@media only screen and (max-width: 1366px) {
  .unlock_growth {
    padding: 72px 36px;

    .main_header {
      font-size: 52px;
      line-height: 62px;
    }

    .desc_text {
      font-size: 18px;
      line-height: 24px;
      margin-bottom: 45px;
    }

    .photo_gallery {
      .photo_container {
        width: calc(25% - 20px);
        border-radius: 0px 40px 0px 40px;
      }

      .photo_container:nth-child(1) {
        gap: 8px;
      }

      .photo_container:nth-child(3) {
        gap: 8px;
      }

      .photo_container:nth-child(4) {
        gap: 8px;
      }
    }
  }
}

@media only screen and (max-width: 1180px) {
  .unlock_growth {
    padding: 48px 24px;

    .main_header {
      font-size: 45px;
      line-height: 55px;
      margin: 10px 0px 16px;
    }

    .desc_text {
      font-size: 16px;
      line-height: 20px;
      margin-bottom: 35px;
    }

    .photo_gallery {
      .photo_container {
        width: calc(25% - 15px);
        border-radius: 0px 30px 0px 30px;
      }
    }
  }
}

@media only screen and (max-width: 1024px) {
  .unlock_growth {
    padding: 80px 40px;
    min-height: 100dvh;

    .eclipse_one {
      width: 90vw;
      top: -7vw;
    }

    .main_header {
      font-size: 6vw;
      line-height: unset;
      margin: 0px 0px 4vw;
    }

    .desc_text {
      font-size: 2.2vw;
      line-height: 3.2vw;
      margin-bottom: 5vw;

      br {
        display: none;
      }
    }

    .view_products_button {
      font-size: 2.5vw;
      padding: 1.5vw 3vw;
    }

    .photo_gallery {
      gap: 40px;
      justify-content: center;
      align-items: center;
      background-color: white;

      .photo_container {
        width: calc(50% - 20px);
        border-radius: 0px 40px 0px 40px;
        background-color: white;

        .img_container {
          background-color: white;
        }
      }

      .photo_container:nth-child(1) {
        gap: 2vw;
      }

      .photo_container:nth-child(3) {
        gap: 2vw;
      }

      .photo_container:nth-child(4) {
        gap: 2vw;
      }
    }
  }
}

@media only screen and (max-width: 667px) {
  .unlock_growth {
    padding: 40px 15px;
    min-height: 100dvh;

    .eclipse_one {
      width: 100vw;
    }

    .main_header {
      font-size: 6.3vw;
      margin: 0px 0px 3vw;
    }

    .desc_text {
      font-size: 3.3vw;
      line-height: 4.3vw;
      margin-bottom: 6vw;
    }

    .view_products_button {
      font-size: 3.5vw;
      padding: 2vw 4vw;
    }

    .photo_gallery {
      gap: 20px;

      .photo_container {
        width: calc(50% - 10px);
        border-radius: 0px 30px 0px 30px;
      }
    }
  }
}
