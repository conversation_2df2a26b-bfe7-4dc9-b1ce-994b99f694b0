import classes from "./styles.module.scss";
import Image from "next/image";
import CareerCard from "../../../components/careerCard";
import { useState, useEffect } from "react";
// import Tab from "@/components/tab";
// import Table from "@/components/table";
import Vision from "../../../assets/images/vision.webp";
import Mission from "../../../assets/images/mission.webp";
import WhoWeAre from "../../../assets/images/whoWeAre.webp";

const Department = () => {

    const cardsData = [
        {
            id: 1,
            title: "Who we are",
            description: "Our team of highly qualified experts is committed to provide unmatched IT services that enable companies to prosper in the quickly changing digital landscape for a better future. We have been working with the clients around the world and making them reach their goals effectively and efficiently.",
            image: <Image src={WhoWeAre} width={560} height={429} alt="banner" />,
            buttonText: "Know More"
        },
        {
            id: 2,
            title: "Our Mission",
            description: "The mission of our company is to provide reliable, cutting-edge IT solutions that enable businesses to prosper in the digital age. Our primary goal is to provide customized technological services that boost efficiency, inspire growth, and promote long-term success. We also put a high value on maintaining a high standard of customer satisfaction and constant product development.",
            image: <Image src={Mission} width={560} height={429} alt="banner" />,
            buttonText: "Explore"
        },
        {
            id: 3,
            title: "Our Vision",
            description: "Our vision is to become a prominent IT company that drives innovation and provides revolutionary digital solutions to help companies all over the world. Our mission is to provide a vibrant, friendly workplace that promotes growth by the use of cutting-edge technology, outstanding  customer service, and a commitment to excellence in all of our project endeavors.",
            image: <Image src={Vision} width={560} height={429} alt="banner" />,
            buttonText: "Learn More"
        },
        // {
        //     id: 4,
        //     title: "Life at [cB]",
        //     description: "We believe in fostering a work environment that blends professional growth with personal well-being. Our team is a diverse group of talented individuals who are passionate about technology and eager to solve complex challenges.",
        //     image: <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}lifeAtCB.webp`} width={560} height={429} alt="banner" />,
        //     buttonText: "Apply Now"
        // }
    ];

    const [activeCard, setActiveCard] = useState(cardsData[0].id);
    const [scrollDirection, setScrollDirection] = useState(null);
    const [lastScrollY, setLastScrollY] = useState(0);

    useEffect(() => {
        const handleScroll = () => {
            const currentScrollY = window.scrollY;

            if (currentScrollY > lastScrollY) {
                setScrollDirection("down");
            } else {
                setScrollDirection("up");
            }

            setLastScrollY(currentScrollY);
        };

        window.addEventListener("scroll", handleScroll);

        return () => {
            window.removeEventListener("scroll", handleScroll);
        };
    }, [lastScrollY]);

    return(
        <div className={classes.department}>
            <div className={classes.career_card_section}>
                {cardsData.map((card) => (
                    <CareerCard
                        key={card.id}
                        id={card.id}
                        activeCard={activeCard}
                        setActiveCard={setActiveCard}
                        scrollDirection={scrollDirection}
                        title={card.title}
                        description={card.description}
                        image={card.image}
                        buttonText={card.buttonText}
                    />
                ))}
            </div>
        </div>
    )
}

export default Department;