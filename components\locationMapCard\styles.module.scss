@import "../../assets/css/global.scss";

.card_container {
    width: 200px;
    height: 800px;
    background-color: rgba(255, 255, 255, 0.2);
    display: flex;
    transition: width 400ms;

    .card_header {
        width: 200px;
        height: 800px;
        background-color: #252525;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: width 400ms, background-color 400ms;
        border: none;
        outline: none;

        p {
            color: rgba(255, 255, 255, 0.5);
            transform: rotate(-90deg);
            font-size: 60px;
            font-family: $lexRegular;
            transition: font-size 400ms, color 400ms;
        }
    }

    .card_content {
        display: none;
        position: relative;
        width: calc(100% - 250px);
        height: 800px;
        overflow: hidden;

        .map_image_container{
            width: 100%;
            height: 100%;

            img{
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }

        .some_texts {
            position: absolute;
            width: 350px;
            right: 0;
            bottom: 0;
            z-index: 999;
            padding: 40px;
            background: linear-gradient(to right, black 50%, transparent 50%);

            .some_texts_header {
                color: white;
                font-size: 18px;
                font-family: $lexSemiBold;
                margin-bottom: 10px;
            }

            .address_text {
                color: rgba(255, 255, 255, 0.5);
                margin-bottom: 10px;
                font-size: 18px;
                font-family: $lexLight;
            }
        }
    }

    &.open {
        width: calc(100% - 460px);

        .card_header {
            width: 250px;
            height: 800px;
            background-color: #890000;

            p {
                font-size: 80px;
                color: white;
            }
        }

        .card_content {
            display: block;
        }
    }
}

@media only screen and (max-width: 1366px){
    .card_container {
        width: 180px;
        height: 85vh;
    
        .card_header {
            width: 180px;
            height: 85vh;
    
            p {
                font-size: 50px;
            }
        }
    
        .card_content {
            width: calc(100% - 150px);
            height: 85vh;
    
            .some_texts {
                width: 320px;
                padding: 35px;
    
                .some_texts_header {
                    font-size: 16px;
                    margin-bottom: 8px;
                }
    
                .address_text {
                    font-size: 16px;
                    margin-bottom: 8px;
                }
            }
        }
    
        &.open {
            width: calc(100% - 460px);
    
            .card_header {
                width: 220px;
                height: 85vh;
    
                p {
                    font-size: 65px;
                }
            }
    
            .card_content {
                display: block;
            }
        }
    }
}
