// .map_container{
//     width: 100%;
//     background-color: black;
//     display: flex;
//     align-items: center;
//     justify-content: center;

//     svg{
//         background-color: green;
//         width: 80vw;
//         height: 100vh;
//         display: flex;
//         align-items: center;
//         justify-content: center;
//         path{
//             width: 10px !important;
//         }
//     }
// }

/* styles.module.scss */
.button {
    background-color: lightgray;
    padding: 10px 20px;
    border: 1px solid #ccc;
    cursor: pointer;
    transition: background-color 0.3s ease;
  
    // &:hover {
    //   background-color: #ddd;
    // }
  }
  
  .activeButton {
    background-color: green;
    color: white;
  }
  
  .divContent {
    margin-top: 20px;
    padding: 20px;
    border: 1px solid #ccc;
    border-radius: 5px;
    background-color: #f9f9f9;
  }
  