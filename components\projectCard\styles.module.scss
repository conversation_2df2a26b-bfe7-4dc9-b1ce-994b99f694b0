@import "../../assets/css/global.scss";

.project_card{
    width: 100%;
    height: auto;

    .card_image{
        width: 100%;
        height: auto;
        aspect-ratio: 437/263;
        margin-bottom: 30px;
        border-radius: 10px;
    }

    .card_header{
        width: 100%;
        height: auto;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 15px;
        
        p{
            color: white;
            font-size: 24px;
            font-family: $lexMedium;
        }

        img{
            width: 15px;
            height: auto;
        }
    }

    .desc_text{
        color: white;
        font-size: 14px;
        font-family: $lexLight;
        // overflow: hidden;
        // display: -webkit-box;
        // -webkit-line-clamp: 5;
        // line-clamp: 5;
        // -webkit-box-orient: vertical;
    }
}

@media only screen and (max-width: 1366px){
    .card{
        .card_image{
            margin-bottom: 20px;
            border-radius: 8px;
        }
    
        .card_header{
            margin-bottom: 10px;
            
            p{
                font-size: 20px;
            }
    
            img{
                width: 13px;
            }
        }
    
        .desc_text{
            font-size: 13px;
            font-family: $lexLight;
        }
    }
}

@media only screen and (max-width: 1180px){
    .card{
        .card_image{
            margin-bottom: 15px;
            border-radius: 8px;
        }
    
        .card_header{
            margin-bottom: 10px;
            
            p{
                font-size: 17px;
            }
    
            img{
                width: 11px;
            }
        }
    
        .desc_text{
            font-size: 11px;
            font-family: $lexThin;
            letter-spacing: 0.5px;
        }
    }   
}

@media only screen and (max-width: 980px){
    .card{
        .card_header{
            
            p{
                font-size: 14px;
            }
    
            img{
                width: 8px;
            }
        }
    
        .desc_text{
            font-size: 9px;
        }
    }   
}

@media only screen and (max-width: 667px){
    .card{
        .card_header{
            p{
                font-size: 20px;
            }
    
            img{
                width: 14px;
            }
        }
    
        .desc_text{
            font-size: 13px;
            line-height: unset;
            letter-spacing: 1px;
        }
    }
}