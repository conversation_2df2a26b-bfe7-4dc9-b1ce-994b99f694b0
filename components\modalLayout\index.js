import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import Box from '@mui/material/Box';
import Modal from '@mui/material/Modal';
import CloseRoundedIcon from '@mui/icons-material/CloseRounded';
import Slide from '@mui/material/Slide';
import classes from './styles.module.scss';

const ModalLayout = (props) => {
  // Disable body scroll when modal is open
  useEffect(() => {
    if (props.open) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }

    // Cleanup when the modal is closed or component is unmounted
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [props.open]);

  return (
    <div className={classes.modal}>
      <Modal
        open={props.open}
        onClose={props.close}
        style={{ zIndex: 2147483646 }}
        BackdropProps={{
          style: {
            backgroundColor: 'transparent',
          },
        }}
      >
        <Slide 
          direction={props.slideDirection || 'left'} 
          in={props.open} 
          mountOnEnter 
          unmountOnExit
          timeout={500} 
        >
          <Box className={classes.modal_container}>
            <div className={classes.modal_content}>
              {!props.hideHeader && (
                <div className={classes.modal_header}>
                  <div className={classes.left}>
                    <p>{props.title}</p>
                  </div>
                  <CloseRoundedIcon onClick={props.close} />
                </div>
              )}
              <div className={classes.modal_body}>
                {props.children}
              </div>
            </div>
          </Box>
        </Slide>
      </Modal>
    </div>
  );
};

ModalLayout.propTypes = {
  open: PropTypes.bool.isRequired,
  close: PropTypes.func.isRequired,
  hideHeader: PropTypes.bool,
  title: PropTypes.string,
  children: PropTypes.node,
  slideDirection: PropTypes.oneOf(['left', 'right', 'up', 'down']),
};

export default ModalLayout;
