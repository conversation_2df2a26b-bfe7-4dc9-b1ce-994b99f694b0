import PropTypes from 'prop-types';
import { Suspense } from "react";
import Layout from "./layout";
import Script from 'next/script';
import Head from 'next/head';
import "../styles/global.css";

function MyApp({ Component, pageProps, ...rest }) {

    return (
        <>
            {/* SEO */}
            <Head>
                <title>Codebucket Solutions Pvt. Ltd. | IT Services in Patna, India</title>
                <meta name="description" content="Codebucket - Your go-to resource for code and development." />
                <meta name="viewport" content="width=device-width, initial-scale=1.0" />
                <meta name="google-site-verification" content="gXW21po5W-BXjX7XJaSvb87fzbtc0kL18lAL1ASNXY0" />
                <link rel='icon' href='/bigCB.ico' type="image/x-icon" />
            </Head>
            {/* Other scripts */}
            <Script
                strategy="lazyOnload"
                src="https://www.googletagmanager.com/gtag/js?id=G-CYZC7Z8D2E"
            />
            <script async src="https://instagram.com/static/bundles/es6/EmbedSDK.js/47c7ec92d91e.js"></script>

                <Layout>
                    <Suspense fallback={<div>Loading...</div>}>
                        <Component {...pageProps} />
                    </Suspense>
                </Layout>
        </>
    );
}

MyApp.propTypes = {
    Component: PropTypes.elementType.isRequired,
    pageProps: PropTypes.object.isRequired,
};

export default MyApp;