.development_design_analytics_two{
    width: 100%;
    min-height: 100vh;
    background-color: black;
    padding: 80px 40px;
    border-radius: 30px;
}

@media only screen and (max-width: 1366px){
    .development_design_analytics_two{
        padding: 72px 36px;
    }
}

@media only screen and (max-width: 1180px){
    .development_design_analytics_two{
        padding: 48px 24px;
    }
}

@media only screen and (max-width: 1024px){
    .development_design_analytics_two{
        padding: 80px 40px;
    }
}

@media only screen and (max-width: 667px){
    .development_design_analytics_two{
        padding: 40px 15px;
    }
}