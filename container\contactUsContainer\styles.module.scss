@import "../../assets/css/global.scss";

.contact_us_container{
    width: 100%;
    height: auto;
    padding: 80px 40px;
    background-color: white;

    .contact_us_content{
        width: 100%;
        height: auto;
        background-color: white;

        .header{
            width: 100%;
            height: auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 5px solid #970000;
            margin-bottom: 50px;
            background-color: white;

            p{
                font-size: 128px;
                font-family: $ubuntuLight;
            }

            img{
                width: 65px;
                height: auto;
                margin-right: 50px;
            }
        }

        .content{
            width: 100%;
            padding: 0px 70px;
            height: auto;
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            background-color: white;

            .left_form_content,
            .right_form_content{
                width: 45%;
                height: auto;
                background-color: white;
            }

            .left_form_content{
                form{
                    .line{
                        display: flex;
                        gap: 40px;
                        margin-bottom: 30px;
                    }

                    .msg_button{
                        display: flex;
                        align-items: center;
                        gap: 10px;

                        img{
                            width: 20px;
                            height: auto;
                            margin-top: 3px;
                        }
                    }

                    .msg_button_sent{
                        background: unset;
                        background-color: #970000;

                         img{
                            display: none;
                        }
                    }
                }
            }

            .right_form_content{
                .right_header{
                    font-size: 48px;
                    font-family: $lexLight;
                    margin-bottom: 15px;
                }

                .desc_text{
                    font-size: 18px;
                    font-family: $lexLight;
                    margin-bottom: 40px;
                }

                form{
                    margin-bottom: 120px;

                    .line{
                        display: flex;
                        gap: 40px;
                        margin-bottom: 30px;
                        background-color: white;
                    }

                    .msg_button{
                        display: flex;
                        align-items: center;
                        gap: 10px;

                        img{
                            width: 20px;
                            height: auto;
                            margin-top: 3px;
                        }
                    }

                    .msg_button_sent{
                        background: unset;
                        background-color: #970000;

                        img{
                            display: none;
                        }
                    }
                }

                .contact_us_header{
                    font-size: 20px;
                    font-family: $lexLight;
                    margin-bottom: 20px;
                }

                .main, .phone_no{
                    font-size: 18px;
                    font-family: $lexLight;
                    margin-bottom: 10px;
                }

                .social_links{
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    gap: 10px;
                    margin-top: 60px;
                    background-color: white;

                    img{
                        width: 20px;
                        height: auto;
                    }
                }
            }
        }
    }
}

.error {
  color: red;
  font-size: 12px;
  margin-top: 4px;
}

@media only screen and (max-width: 1366px){
    .contact_us_container{
        padding: 72px 36px;
        
        .contact_us_content{
            .header{
                border-bottom: 4px solid #970000;
                margin-bottom: 40px;
    
                p{
                    font-size: 110px;
                }
    
                img{
                    width: 60px;
                    margin-right: 45px;
                }
            }
    
            .content{
                padding: 0px 60px;
    
                .left_form_content{
                    form{
                        .line{
                            gap: 30px;
                            margin-bottom: 25px;
                        }
                    }
                }
    
                .right_form_content{
                    .right_header{
                        font-size: 42px;
                        margin-bottom: 10px;
                    }
    
                    .desc_text{
                        font-size: 16px;
                        margin-bottom: 30px;
                    }
    
                    form{
                        margin-bottom: 100px;
    
                        .line{
                            margin-bottom: 25px;
                        }
                    }
    
                    .contact_us_header{
                        font-size: 18px;
                        margin-bottom: 15px;
                    }
    
                    .main, .phone_no{
                        font-size: 16px;
                    }
    
                    .social_links{
                        margin-top: 50px;
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 1180px){
    .contact_us_container{
        padding: 48px 24px;
        
        .contact_us_content{
            .header{
                border-bottom: 3px solid #970000;
                margin-bottom: 35px;
    
                p{
                    font-size: 95px;
                }
    
                img{
                    width: 50px;
                    margin-right: 40px;
                }
            }
    
            .content{
                padding: 0px 50px;
    
                .left_form_content{
                    form{
                        .line{
                            gap: 25px;
                            margin-bottom: 20px;
                        }
    
                        .msg_button{
                            gap: 6px;
    
                            img{
                                width: 16px;
                            }
                        }
                    }
                }
    
                .right_form_content{
                    .right_header{
                        font-size: 38px;
                        margin-bottom: 7px;
                    }
    
                    .desc_text{
                        font-size: 15px;
                        margin-bottom: 25px;
                    }
    
                    form{
                        margin-bottom: 80px;
    
                        .line{
                            gap: 25px;
                            margin-bottom: 20px;
                        }
    
                        .msg_button{
                            gap: 6px;
    
                            img{
                                width: 16px;
                            }
                        }
                    }
    
                    .contact_us_header{
                        font-size: 16px;
                        margin-bottom: 15px;
                    }
    
                    .main, .phone_no{
                        font-size: 15px;
                        margin-bottom: 6px;
                    }
    
                    .social_links{
                        margin-top: 40px;
    
                        img{
                            width: 18px;
                        }
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 980px){
    .contact_us_container{
        padding: 40px 20px;
        
        .contact_us_content{
            .header{
                p{
                    font-size: 85px;
                }
    
                img{
                    width: 40px;
                    margin-right: 35px;
                }
            }
    
            .content{
                padding: 0px 40px;
    
                .left_form_content{
                    form{
                        .line{
                            gap: 20px;
                        }
    
                        .msg_button{
                            gap: 5px;
    
                            img{
                                width: 14px;
                                margin-top: 1px;
                            }
                        }
                    }
                }
    
                .right_form_content{
                    .right_header{
                        font-size: 32px;
                        margin-bottom: 5px;
                    }
    
                    .desc_text{
                        font-size: 14px;
                        margin-bottom: 20px;
                    }
    
                    form{
                        margin-bottom: 60px;
    
                        .line{
                            gap: 20px;
                        }
    
                        .msg_button{
                            gap: 5px;
    
                            img{
                                width: 14px;
                                margin-top: 1px;
                            }
                        }
                    }
    
                    .contact_us_header{
                        font-size: 15px;
                        margin-bottom: 10px;
                    }
    
                    .main, .phone_no{
                        font-size: 14px;
                        margin-bottom: 4px;
                    }
    
                    .social_links{
                        margin-top: 20px;
    
                        img{
                            width: 16px;
                        }
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 780px){
    .contact_us_container{
        padding: 32px 16px;
        
        .contact_us_content{
            .header{
                border-bottom: 2px solid #970000;
    
                p{
                    font-size: 65px;
                }
    
                img{
                    width: 35px;
                    margin-right: 30px;
                }
            }
    
            .content{
                padding: 0px 30px;
    
                .left_form_content{
                    form{
                        .line{
                            gap: 15px;
                            margin-bottom: 15px;
                        }
                    }
                }
    
                .right_form_content{
                    .right_header{
                        font-size: 27px;
                        margin-bottom: 3px;
                    }
    
                    .desc_text{
                        font-size: 13px;
                        margin-bottom: 15px;
                    }
    
                    form{
                        margin-bottom: 50px;
    
                        .line{
                            gap: 15px;
                            margin-bottom: 15px;
                        }
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 667px){
    .contact_us_container{
        padding: 32px 15px;
        
        .contact_us_content{
            .header{
                p{
                    font-size: 9vw;
                }
    
                img{
                    width: 5vw;
                    margin-right: 4vw;
                }
            }
    
            .content{
                padding: 0px 4vw;
                flex-direction: column;
    
                .left_form_content,
                .right_form_content{
                    width: 100%;
                }
    
                .left_form_content{
                    margin-bottom: 8vw;

                    form{
                        .line{
                            gap: 5vw;
                            margin-bottom: 5vw;
                        }
    
                        .msg_button{
                            gap: 1vw;
    
                            img{
                                width: 3.5vw;
                                margin-top: 1px;
                            }
                        }
                    }
                }
    
                .right_form_content{
                    .right_header{
                        font-size: 27px;
                        margin-bottom: 3px;
                    }
    
                    .desc_text{
                        font-size: 13px;
                        margin-bottom: 15px;
                    }
    
                    form{
                        margin-bottom: 50px;
    
                        .line{
                            gap: 5vw;
                            margin-bottom: 5vw;
                        }
    
                        .msg_button{
                            gap: 1vw;
    
                            img{
                                width: 3.5vw;
                                margin-top: 1px;
                            }
                        }
                    }
                }
            }
        }
    }
}