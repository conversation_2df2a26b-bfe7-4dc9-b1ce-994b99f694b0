name: <PERSON>arQube Scan Job

on:
  # Manually trigger anytime
  workflow_dispatch:

  # Automatically every 12 hours
  schedule:
    - cron: '0 */12 * * *'  # Every 12 hours

jobs:

  SonarQube-QualityGate:
    name: SonarQube-QualityGate
    runs-on: sonarqube-scanner
    permissions:
      contents: read

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          ref: 'stage'

      - name: Run SonarQube Scan
        uses: sonarsource/sonarqube-scan-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
