@import "../../../assets/css/global.scss";

.technology_container{
    min-height: 100vh;

    .technology_content{
        background-color: black;
        width: 100%;

        .content{
            width: 100%;
            height: 100vh;
            position: sticky;
            top: 0;
            display: flex;
            align-items: center;

            .framer_wrapper{
                .animation_content{
                    width: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 10vw;

                    .left_part{
                        width: 38%;
            
                        .head_desc_section{
                            margin-bottom: 40px;
            
                            .head{
                                font-family: $ubuntuRegular;
                                font-size: 60px;
                                color: rgba(255, 255, 255, 0.50);
                                margin-bottom: 10px;
                            }
            
                            .desc{
                                font-size: 20px;
                                font-family: $lexLight;
                                line-height: 30px;
                                color: rgba(255, 255, 255, 0.50);
                                animation: fadeIn 1s forwards;
                            }

                            @keyframes fadeIn {
                                0% {
                                  opacity: 0;
                                }
                                100% {
                                  opacity: 1;
                                }
                              }
                        }
                    }
            
                    .right_part{
                        width: 35vw;
                        height: auto;
                        display: flex;
                        justify-content: center;
                        flex-wrap: wrap;
                        gap: 40px;
                        position: relative;
            
                        .card_container{
                            width: 16vw;
                            height: 19.5vw;

                            &:nth-child(1){
                                animation: changePosOne 1s forwards;
                                opacity: 0;
                            }
                            @keyframes changePosOne {
                                0%{
                                    opacity: 0;
                                    transform: translate(100%, 100%);
                                }

                                100%{
                                    opacity: 1;
                                    transform: translate(0%, 0%);
                                }
                            }

                            &:nth-child(2){
                                animation: changePosTwo 1s forwards;
                                opacity: 0;
                            }
                            @keyframes changePosTwo {
                                0%{
                                    opacity: 0;
                                     transform: translate(-50%, 50%);
                                }

                                100%{
                                    opacity: 1;
                                    transform: translate(0%, 0%);
                                }
                            }

                            &:nth-child(3){
                                animation: changePostThree 1s forwards;
                                opacity: 0;
                            }
                            @keyframes changePostThree {
                                0%{
                                    opacity: 0;
                                    transform: translate(50%, -50%);
                                }

                                100%{
                                    opacity: 1;
                                    transform: translate(0%, 0%);
                                }
                            }

                            &:nth-child(4){
                                animation: changePostFour 1s forwards;
                                opacity: 0;
                            }
                            @keyframes changePostFour {
                                0%{
                                    opacity: 0;
                                    transform: translate(-100%, -100%);
                                }

                                100%{
                                    opacity: 1;
                                    transform: translate(0%, 0%);
                                }
                            }

                            
                            
            
                            .card{
                                width: 100%;
                                height: 100%;
                                aspect-ratio: 313/343;
                                border-radius: 7px;
                                background-color: rgb(255, 255, 255);
                                padding: 5px;
                                overflow: hidden;
                                opacity: 0.5;
                                transition: 300ms;

                                &:hover{
                                    opacity: 1;
                                }
            
                                .card_image{
                                    width: 100%;
                                    height: auto;
                                    border-radius: 7px;
                                    aspect-ratio: 300/185;
                                    object-fit: cover;
                                }
                                .nav_header{
                                    display: flex;
                                    align-items: center;
                                    justify-content: space-between;
                                    padding: 5px;
                                    margin: 0.5vw 0vw;
            
                                    p{
                                        font-size: 1vw;
                                        font-family: $lexRegular;
                                    }
            
                                    img{
                                        width: 1vw;
                                        height: auto;
                                        transform: rotate(-45deg);
                                    }
                                }
            
                                .desc_text{
                                    padding: 0vw 0.7vw;
                                    font-size: 0.74vw;
                                    font-family: $lexLight;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 1366px){
    .technology_container{
        .technology_content{
            .content{
                .framer_wrapper{
                    .animation_content{
                        gap: 10vw;
                        .left_part{
                            .head_desc_section{
                                margin-bottom: 20px;
                
                                .head{
                                    font-size: 55px;
                                    margin-bottom: 10px;
                                }
                
                                .desc{
                                    font-size: 18px;
                                    line-height: 24px;
                                }
                            }
                        }
                
                        .right_part{
                            gap: 30px;
                
                            .card_container{
                                .card{
                                    aspect-ratio: unset;
                                    padding: 5px;
                                    overflow: hidden;
                
                                    .desc_text{
                                        padding: 0vw 0.7vw;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 1280px){
    .technology_container{
        .technology_content{
            .content{
                .framer_wrapper{
                    .animation_content{
                        gap: 6vw;
                        .left_part{
                            .head_desc_section{
                                .head{
                                    font-size: 48px;
                                }
                
                                .desc{
                                    font-size: 16px;
                                    line-height: 22px;
                                }
                            }
                        }
                
                        .right_part{
                            gap: 30px;
                
                            .card_container{
                                .card{
                                    aspect-ratio: unset;
                                    padding: 5px;
                                    overflow: hidden;
                
                                    .desc_text{
                                        padding: 0vw 0.7vw;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 667px){
    .technology_container{
        .technology_content{
            .content{
                .framer_wrapper{
                    .animation_content{
                        display: block;

                        .left_part{
                            width: 100%;                            
                            padding: 10px 15px;

                            .head_desc_section{
                                margin-bottom: 3vw;

                                .head{
                                    font-size: 7vw;
                                    margin-bottom: 1vw;
                                }

                                .desc{
                                    font-size: 3vw;
                                    line-height: 4.2vw;
                                }
                            }
                        }

                        .right_part{
                            width: 100%;
                            padding: 10px 15px;
                            gap: 2vw;

                            .card_container{
                                width: calc(50% - 1vw);
                                height: 22vh;

                                .card{
                                    position: relative;
                                    padding: 0;
                                    display: flex;
                                    flex-direction: column;
                                    justify-content: flex-end;
                                    opacity: 1;

                                    .card_image{
                                        width: 100%;
                                        height: auto;
                                        position: absolute;
                                        top: 0;
                                        left: 0;
                                    }

                                    .nav_header{
                                        padding: 2px 5px;
                                        margin: 0;
                                        background-color: white;
                                        position: relative;
                
                                        p{
                                            font-size: 3.5vw;
                                        }
                
                                        img{
                                            width: 4vw;
                                            transform: rotate(-45deg);
                                        }
                                    }
                
                                    .desc_text{
                                        padding: 1vw;
                                        font-size: 2.6vw;
                                        line-height: unset;
                                        background-color: black;
                                        color: white;
                                        overflow: hidden !important;
                                        display: -webkit-box;
                                        -webkit-line-clamp: 3;
                                        line-clamp: 3;
                                        -webkit-box-orient: vertical;
                                        position: relative;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}