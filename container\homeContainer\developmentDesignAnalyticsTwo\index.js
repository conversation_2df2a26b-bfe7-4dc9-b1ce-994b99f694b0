import classes from "./styles.module.scss";
import ServicesCardTwo from "../../../components/servicesCardTwo";
import { useRef } from "react";
import { useScroll } from "framer-motion";
import { projects } from "./data";
import TechCard from "../../../components/techCard";

const DevelopmentDesignAnalyticsTwo = () =>{

    const container = useRef(null);
    const { scrollYProgress } = useScroll({
        target: container,
        offset: ['start start', 'end end']
    });

    return(
        <div ref={container} className={classes.development_design_analytics_two}>

             {
                projects.map((project, i) => {
                    const targetScale = 1 - ((projects.length - i) * 0.09);
                    const isLastCard = i === projects.length - 1; 

                    return (
                        <ServicesCardTwo 
                            key={project.id || i} 
                            i={i} 
                            {...project} 
                            progress={scrollYProgress} 
                            range={[i * 0.50, 1]} 
                            targetScale={targetScale} 
                            isLastCard={isLastCard} 
                            tech_cards={
                                project.tech_cards.map((tech, index) => (
                                    <TechCard 
                                        key={tech.tech_brand || index}
                                        tech_brand={tech.tech_brand} 
                                        image={tech.image} 
                                    />
                                ))
                            }
                        />
                    );
                })
            }
        </div>
    )
}

export default DevelopmentDesignAnalyticsTwo;