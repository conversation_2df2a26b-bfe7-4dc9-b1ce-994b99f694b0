import { useState } from 'react';
import PropTypes from 'prop-types';
import classes from './styles.module.scss';

const Tab = ({ tabs, variant, ...props }) => {
    const [activeTab, setActiveTab] = useState(tabs[0].label);

    return (
        <div className={`${classes.tab_container} ${classes[variant]}`} {...props}>
            <div className={classes.tab_header}>
                {tabs.map((tab) => (
                    <button
                        key={tab.label}
                        onClick={() => setActiveTab(tab.label)}
                        className={`${classes.tab_button} ${activeTab === tab.label ? classes.active : ''}`}
                    >
                        {tab.label}
                    </button>
                ))}
            </div>
            <div className={classes.tab_body}>
                {tabs.map((tab) =>
                    activeTab === tab.label && (
                    <div key={tab.label} className={classes.tab_panel}>
                        {tab.content}
                    </div>
                    )
                )}
            </div>
        </div>
    );
};

Tab.propTypes = {
    tabs: PropTypes.arrayOf(
        PropTypes.shape({
            label: PropTypes.string.isRequired,
            content: PropTypes.node.isRequired,
        })
    ).isRequired,
    variant: PropTypes.string,
};

Tab.defaultProps = {
  variant: 'default',
};

export default Tab;