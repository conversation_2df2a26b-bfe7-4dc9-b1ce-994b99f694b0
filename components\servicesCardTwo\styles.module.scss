@import "../../assets/css/global.scss";

.services_card_two_container{
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: sticky;
    top: 0px;

    .services_card_two_content_tab{
        display: none;
    }

    .services_card_two_content{
        width: 100%;
        height: auto;
        background-color: white;
        border-radius: 24px;
        padding: 30px 35px;
        overflow: hidden;
        display: flex;
        justify-content: space-between;
        gap: 8%;

        .left_section{
            position: relative;
            display: flex;
            flex-direction: column;
            padding-bottom: 60px;
            
            .left_sec_top{
                flex-grow: 1;

                .left_section_head{
                    font-size: 100px;
                    font-family: $ubuntuLight;
                    margin-bottom: 20px;
                }
                .technology_cards_container{
                    width: 100%;
                    margin-bottom: 40px;
                    display: flex;
                    flex-wrap: wrap;
                    gap: 10px;
                }
                .desc_text{
                    font-size: 20px;
                    line-height: 28px;
                    font-family: $lexLight;
                    margin-bottom: 40px;
                }
            }

            .find_out_more{
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 10px;
                flex-shrink: 0;
                width: 200px;

                img{
                    width: 20px;
                    height: auto;
                }
            }
        }

        .right_section{
            min-width: 528px;
            max-width: 528px;
            aspect-ratio: 428/459;
            overflow: hidden;
            border-radius: 0px 45px 0px 0px;
            background-color: white;

            img{
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }
    }

    .services_card_two_content_mobile{
        display: none;
    }
}

@media only screen and (max-width: 1536px){
    .services_card_two_container{
        .services_card_two_content{
    
            .right_section{
                min-width: 428px;
                max-width: 428px;
            }
        }
    }
}

@media only screen and (max-width: 1366px){
    .services_card_two_container{
        .services_card_two_content{
            border-radius: 22px;
            padding: 25px 30px;
            gap: 6%;
    
            .left_section{
                .left_sec_top{
                    .left_section_head{
                        font-size: 95px;
                        margin-bottom: 15px;
                    }
                    .technology_cards_container{
                        margin-bottom: 35px;
                    }
                    .desc_text{
                        font-size: 18px;
                        line-height: 26px;
                        margin-bottom: 35px;
                    }
                }
            }
    
            .right_section{
                min-width: 400px;
                max-width: 400px;
                border-radius: 0px 40px 0px 0px;
            }
        }
    }
}


@media only screen and (max-width: 1280px){
    .services_card_two_container{
        .services_card_two_content{
            border-radius: 20px;
            padding: 20px 25px;
            gap: 6%;
    
            .left_section{
                .left_sec_top{
                    .left_section_head{
                        font-size: 90px;
                    }
                    .technology_cards_container{
                        margin-bottom: 30px;
                    }
                    .desc_text{
                        font-size: 17px;
                        line-height: 25px;
                        margin-bottom: 30px;
                    }
                }
            }
    
            .right_section{
                min-width: 370px;
                max-width: 370px;
                border-radius: 0px 40px 0px 0px;
            }
        }
    }
}

@media only screen and (max-width: 1180px){
    .services_card_two_container{
        .services_card_two_content{
    
            .left_section{
                .left_sec_top{
                    .left_section_head{
                        font-size: 85px;
                    }
                    .technology_cards_container{
                        margin-bottom: 25px;
                    }
                    .desc_text{
                       
                        margin-bottom: 25px;
                    }
                }
            }
    
            .right_section{
                min-width: 350px;
                max-width: 350px;
                border-radius: 0px 40px 0px 0px;
            }
        }
    }
}

@media only screen and (max-width: 1080px){
    .services_card_two_container{
        .services_card_two_content{
    
            .left_section{
                .left_sec_top{
                    .left_section_head{
                        font-size: 80px;
                        margin-bottom: 10px;
                    }
                    .technology_cards_container{
                        margin-bottom: 20px;
                        gap: 8px;
                    }
                }
            }
    
            .right_section{
                min-width: 330px;
                max-width: 330px;
                border-radius: 0px 40px 0px 0px;
            }
        }
    }
}

@media only screen and (max-width: 1024px){
    .services_card_two_container{
        min-height: 100dvh;

        .services_card_two_content{
            display: none;
        }

        .services_card_two_content_tab{
            width: 100%;
            height: auto;
            background-color: white;
            border-radius: 24px;
            padding: 30px;
            overflow: hidden;
            display: block;

            .image_section{
                margin-bottom: 2vw;

                img{
                    border-radius: 25px;
                    height: 35vh;
                    width: 100%;
                    object-fit: cover;
                }
            }

            .card_head{
                font-size: 7vw;
                font-family: $ubuntuLight;
                margin-bottom: 2vw;
            }

            .tech_cards{
                display: flex;
                flex-wrap: wrap;
                gap: 2vw;
                justify-content: flex-start;
                margin-bottom: 5vw;
            }

            .card_desc{
                font-size: 2.8vw;
                margin-bottom: 3vw;
            }

            .find_out_more_tab{
                font-size: 2.4vw;
                padding: 1.8vw 3vw;
                float: right;

                img{
                    margin-left: 1.2vw;
                }
            }
        }
    }
}

@media only screen and (max-width: 667px){
    .services_card_two_container{
        .services_card_two_content_tab{
            border-radius: 18px;
            padding: 15px;

            .image_section{
                margin-bottom: 2vw;

                .card_main_image{
                    border-radius: 18px;
                    height: 30vh;
                }
            }

            .card_head{
                font-size: 9vw;
                margin-bottom: 2vw;
            }

            .tech_cards{
                gap: 2vw;
                margin-bottom: 3vw;
            }

            .card_desc{
                font-size: 3vw;
                margin-bottom: 5vw;
            }

            .find_out_more_tab{
                font-size: 3vw;
                padding: 2vw 3vw;

                img{
                    margin-left: 1vw;
                    width: 4vw;
                    height: auto;
                }
            }
        }
    }
}