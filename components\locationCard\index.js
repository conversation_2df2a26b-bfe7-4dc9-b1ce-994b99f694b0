import React from "react";
import PropTypes from 'prop-types';
import classes from "./styles.module.scss";
import Image from "next/image";

const LocationCard = (props) => {
    return (
        <div className={classes.location_card}>
            <Image className={classes.location_icon} src={`${process.env.NEXT_PUBLIC_CDN_URL}orangeLocation.webp`} width={116} height={170} alt="icon" />
            <p className={classes.location_name}>{props.location_name}</p>
            <p className={classes.location_Address}>{props.location_address}</p>
            <div className={classes.view_link}>
                {props.location_link}
            </div>
        </div>
    );
};

LocationCard.propTypes = {
    location_name: PropTypes.string.isRequired,
    location_address: PropTypes.string.isRequired,
    location_link: PropTypes.node.isRequired,
};

export default LocationCard;
