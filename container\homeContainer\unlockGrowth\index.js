import { useState, useEffect } from "react";
import { motion, useScroll, useTransform } from "framer-motion";
import classes from "./styles.module.scss";
import Button from "@/components/button";
import Image from "next/image";
import { useRouter } from "next/router";

const UnlockGrowth = () => {
  const router = useRouter();
  const handleViewProduct = () => {
    router.push("/product");
  };

  const { scrollY } = useScroll();

  const [marginValues, setMarginValues] = useState(["10vh", "5vh"]);
  useEffect(() => {
    const updateMarginValues = () => {
      if (window.innerWidth <= 667) {
        setMarginValues(["9vh", "3vh"]);
      } else if (window.innerWidth <= 980) {
        setMarginValues(["7vh", "3vh"]);
      } else if (window.innerWidth <= 1080) {
        setMarginValues(["11vh", "5vh"]);
      } else {
        setMarginValues(["12vh", "5vh"]);
      }
    };

    updateMarginValues();
    window.addEventListener("resize", updateMarginValues);

    return () => window.removeEventListener("resize", updateMarginValues);
  }, []);

  const marginTop = useTransform(scrollY, [0, 100], marginValues);
  const rightPosition = useTransform(scrollY, [0, 100], ["-20vw", "10vw"]);
  // const scale = useTransform(scrollY, [0, 100], [1, 0.96]);
  // const opacity = useTransform(scrollY, [0, 100], [1, 0.4]);
  // const margin = useTransform(scrollY, [0, 100], [0, -20])

  return (
    <motion.div
      className={classes.unlock_growth}
      // style={{ scale, opacity, margin }}
    >
      <motion.div
        className={classes.eclipse_one}
        style={{ right: rightPosition }}
      >
        <Image
          src={`${process.env.NEXT_PUBLIC_CDN_URL}EllipseOne.webp`}
          width={1718}
          height={1350}
          alt=""
          style={{ right: rightPosition }}
          priority
        />
      </motion.div>
      <p className={classes.main_header}>
        <span>Unlock growth</span> with <br />
        Innovation-Driven Development
      </p>
      <p className={classes.desc_text}>
        We don’t just develop software, we cultivate groundbreaking solutions
        tailored to your unique business goals. <br />
        We craft customized IT solutions that enhance productivity, streamline
        operations, and maximize ROI.
      </p>
      <Button
        second_class={classes.view_products_button}
        button_text="View Products"
        variant="button_orange"
        onClick={handleViewProduct}
      />

      <motion.div className={classes.photo_gallery} style={{ marginTop }}>
        <div className={classes.photo_container}>
          <div className={classes.img_container}>
            <Image
              src={`${process.env.NEXT_PUBLIC_CDN_URL}image.webp`}
              width={690}
              height={268}
              alt="img"
              priority
            />
          </div>
          <div className={classes.img_container}>
            <Image
              src={`${process.env.NEXT_PUBLIC_CDN_URL}image1.webp`}
              width={690}
              height={370}
              alt="img"
              priority
            />
          </div>
        </div>

        <div className={classes.photo_container}>
          <div className={classes.img_container}>
            <Image
              src={`${process.env.NEXT_PUBLIC_CDN_URL}image2.webp`}
              width={690}
              height={666}
              alt="img"
              priority
            />
          </div>
        </div>

        <div className={classes.photo_container}>
          <div className={classes.img_container}>
            <Image
              src={`${process.env.NEXT_PUBLIC_CDN_URL}image3.webp`}
              width={596}
              height={682}
              alt="img"
              priority
            />
          </div>
        </div>

        <div className={classes.photo_container}>
          <div className={classes.img_container}>
            <Image
              src={`${process.env.NEXT_PUBLIC_CDN_URL}image4.webp`}
              width={690}
              height={370}
              alt="img"
              priority
            />
          </div>
          <div className={classes.img_container}>
            <Image
              src={`${process.env.NEXT_PUBLIC_CDN_URL}image5.webp`}
              width={690}
              height={268}
              alt="img"
              priority
            />
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default UnlockGrowth;
