import classes from "./styles.module.scss";
import Image from "next/image";
import Link from "next/link";
import { Swiper, SwiperSlide } from "swiper/react";
import { FreeMode, Autoplay } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/free-mode';
import 'swiper/css/pagination';
import LocationCard from "@/components/locationCard";

const LifeAtCB = () => {

    return(
        <div className={classes.life_at_cb}>
            <p className={classes.life_at_cb_header}>Life at &#91;cB&#93;</p>
            <p className={classes.life_at_cb_header_desc}>
                We are proud to call these places home. Explore our offices around 
                <br />the world and find the perfect fit for your career journey
            </p>
            <div className={classes.hr_line}></div>

            <div className={classes.our_company_location}>
                <LocationCard 
                    location_name="Patna"
                    location_address={<>Opposite Gate No 93, Digha <br />Danapur Road, Patna - 800013</>}
                    location_link={<Link href={"/contact-us"}>Click to View</Link>}
                />
                <LocationCard 
                    location_name="Registered Office: "
                    location_address={<>HMT Colony,Kalamassery, Kochi, Kerala.</>}
                    location_link={<Link href={"/contact-us"}>Click to View</Link>}
                />
                <LocationCard 
                    location_name="Bangalore"
                    location_address={<>WeWork Prestige Central, 36, <br /> Infantry Road, Bangalore, <br /> Karnantaka 560001</>}
                    location_link={<Link href={"/contact-us"}>Click to View</Link>}
                />
            </div>

            <div className={classes.our_photo_gallery}>
                <Swiper
                    slidesPerView={5}
                    spaceBetween={40}
                    freeMode={true}
                    modules={[FreeMode, Autoplay]}
                    loop={true} 
                    autoplay={{
                        delay: 1500,
                        disableOnInteraction: false,
                    }}

                    breakpoints={{
                        1181: {
                          spaceBetween: 40,
                        },
                        668: {
                          spaceBetween: 20,
                        },
                        300: {
                            spaceBetween: 15,
                            slidesPerView: 4,
                        },
                    }}
                >
                    <SwiperSlide>
                    <div className={classes.image_holder}>
                        <Image
                            className={classes.gallery_photo}
                            src={`${process.env.NEXT_PUBLIC_CDN_URL}whoWeAre.webp`}
                            width={500}
                            height={400}
                            alt="One"
                        />
                    </div>
                    </SwiperSlide>

                    <SwiperSlide>
                    <div className={classes.image_holder}>
                        <Image
                            className={classes.gallery_photo}
                            src={`${process.env.NEXT_PUBLIC_CDN_URL}joinATeamInspire.webp`}
                            width={500}
                            height={400}
                            alt="Three"
                        />
                    </div>
                    </SwiperSlide>

                    <SwiperSlide>
                    <div className={classes.image_holder}>
                        <Image
                            className={classes.gallery_photo}
                            src={`${process.env.NEXT_PUBLIC_CDN_URL}holi.webp`}
                            width={500}
                            height={400}
                            alt="Four"
                        />
                    </div>
                    </SwiperSlide>

                    <SwiperSlide>
                    <div className={classes.image_holder}>
                        <Image
                            className={classes.gallery_photo}
                            src={`${process.env.NEXT_PUBLIC_CDN_URL}celebration.webp`}
                            width={500}
                            height={400}
                            alt="Six"
                        />
                    </div>
                    </SwiperSlide>

                    <SwiperSlide>
                    <div className={classes.image_holder}>
                        <Image
                            className={classes.gallery_photo}
                            src={`${process.env.NEXT_PUBLIC_CDN_URL}celebrationOne.webp`}
                            width={500}
                            height={400}
                            alt="Seven"
                        />
                    </div>
                    </SwiperSlide>

                    <SwiperSlide>
                    <div className={classes.image_holder}>
                        <Image
                            className={classes.gallery_photo}
                            src={`${process.env.NEXT_PUBLIC_CDN_URL}celebrationTwo.webp`}
                            width={500}
                            height={400}
                            alt="Eight"
                        />
                    </div>
                    </SwiperSlide>

                    <SwiperSlide>
                    <div className={classes.image_holder}>
                        <Image
                            className={classes.gallery_photo}
                            src={`${process.env.NEXT_PUBLIC_CDN_URL}sports.webp`}
                            width={500}
                            height={400}
                            alt="Nine"
                        />
                    </div>
                    </SwiperSlide>

                    <SwiperSlide>
                    <div className={classes.image_holder}>
                        <Image
                            className={classes.gallery_photo}
                            src={`${process.env.NEXT_PUBLIC_CDN_URL}event.webp`}
                            width={500}
                            height={400}
                            alt="Ten"
                        />
                    </div>
                    </SwiperSlide>
                </Swiper>
            </div>
        </div>
    )
}

export default LifeAtCB;