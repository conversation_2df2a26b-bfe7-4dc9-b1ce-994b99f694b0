import PropTypes from 'prop-types';
import classes from "./styles.module.scss";

const LocationSlideCard = ({ location_name, map, company_address, contact_num, contact_mail, isExpanded, onClick }) => {
    return (
        <button
            className={`${classes.location_slider_card} ${isExpanded ? classes.expanded : ''}`}
            onClick={onClick} // Attach click handler
        >
            <div className={classes.left_name}>
                <p className={classes.name}>{location_name}</p>
            </div>
            <div className={classes.right_map}>
                <div className={classes.map}>
                    {map}
                </div>
                <div className={classes.address_details}>
                    <p className={classes.text}>{company_address}</p>
                    <p className={classes.text}>{contact_num}</p>
                    <p className={classes.text}>{contact_mail}</p>
                </div>
            </div>
        </button>
    );
};

LocationSlideCard.propTypes = {
    location_name: PropTypes.string.isRequired,
    map: PropTypes.node.isRequired,
    company_address: PropTypes.string.isRequired,
    contact_num: PropTypes.string.isRequired,
    contact_mail: PropTypes.string.isRequired,
    isExpanded: PropTypes.bool.isRequired,
    onClick: PropTypes.func.isRequired,
};

export default LocationSlideCard;
